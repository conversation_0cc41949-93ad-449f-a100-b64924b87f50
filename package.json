{"name": "sd-lal-vue3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode develop", "build": "vite build", "preview": "vite preview", "build:prod": "vite build --mode production", "upload": "node bin/upload_to_cos.cjs", "deploy": "npm run build:prod && npm run upload", "deploy:dev": "vite build --mode development && npm run upload"}, "dependencies": {"@antv/l7": "^2.22.5", "@antv/l7-maps": "^2.22.5", "@antv/l7plot": "^0.5.11", "@vicons/antd": "^0.12.0", "@vicons/carbon": "^0.12.0", "@vicons/fluent": "^0.12.0", "@vicons/ionicons5": "^0.12.0", "@vicons/material": "^0.12.0", "@vicons/tabler": "^0.12.0", "@wecom/jssdk": "^1.3.1", "axios": "^1.7.7", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "mitt": "^3.0.1", "naive-ui": "^2.41.0", "path": "^0.12.7", "pinia": "^2.2.4", "vue": "^3.5.10", "vue-router": "^4.4.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "cos-nodejs-sdk-v5": "^2.15.0", "dotenv": "^16.5.0", "sass": "^1.86.1", "sass-loader": "^14.1.1", "terser": "^5.40.0", "vite": "^5.4.8"}}