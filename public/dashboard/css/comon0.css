﻿@charset "utf-8";

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box
}

*,
body {
  padding: 0px;
  margin: 0px;
  color: #222;
  font-family: "微软雅黑";
}

body {
  background: #000d4a url(../images/bg.jpg) center top;
  background-size: cover;
  color: #666;
  padding-bottom: 30px;
  font-size: .1rem;
}

li {
  list-style-type: none;
}

table {}

i {
  margin: 0px;
  padding: 0px;
  text-indent: 0px;
}

img {
  border: none;
  max-width: 100%;
}

a {
  text-decoration: none;
  color: #399bff;
}

a.active,
a:focus {
  outline: none !important;
  text-decoration: none;
}

ol,
ul,
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  padding: 0;
  margin: 0
}

a:hover {
  color: #06c;
  text-decoration: none !important
}


.clearfix:after,
.clearfix:before {
  display: table;
  content: " "
}

.clearfix:after {
  clear: both
}

.pulll_left {
  float: left;
}

.pulll_right {
  float: right;
}

/*滚动条样式已移至全局样式*/

/***/

.loading {
  position: fixed;
  left: 0;
  top: 0;
  font-size: .3rem;
  z-index: 100000000;
  width: 100%;
  height: 100%;
  background: #1a1a1c;
  text-align: center;
}

.loadbox {
  position: absolute;
  width: 160px;
  height: 150px;
  color: #324e93;
  left: 50%;
  top: 50%;
  margin-top: -100px;
  margin-left: -75px;
}

.loadbox img {
  margin: 10px auto;
  display: block;
  width: 40px;
}

.copyright {
  background: rgba(19, 31, 64, .32);
  border: 1px solid rgba(255, 255, 255, .05);
  line-height: .5rem;
  text-align: center;
  padding-right: 15px;
  bottom: 0;
  color: rgba(255, 255, 255, .7);
  font-size: .16rem;
}

.head {
  height: 1.05rem;
  background: url(../images/head_bg.png) no-repeat center center;
  background-size: 100% 100%;
  position: relative;
  z-index: 20;
}

.head h1 {
  color: #fff;
  text-align: center;
  font-size: .42rem;
  line-height: .75rem;
}

.head h1 img {
  width: 1.5rem;
  display: inline-block;
  vertical-align: middle;
  margin-right: .2rem
}

.weather {
  position: absolute;
  right: .3rem;
  top: 0;
  line-height: .75rem;
}

.weather img {
  width: .37rem;
  display: inline-block;
  vertical-align: middle;
}

.weather span {
  color: rgba(255, 255, 255, .7);
  font-size: .18rem;
  padding-right: .1rem;
}

.mainbox {
  padding: .4rem .4rem 0rem .4rem;
}

.mainbox>ul {
  margin-left: -.4rem;
  margin-right: -.4rem;
}

.mainbox>ul>li {
  float: left;
  padding: 0 .4rem
}

.mainbox>ul>li {
  width: 30%
}

.mainbox>ul>li:nth-child(2) {
  width: 40%
}

.boxall {
  border: 1px solid rgba(25, 186, 139, .17);
  padding: 0 .3rem .3rem .3rem;
  background: rgba(255, 255, 255, .04) url(../images/line.png);
  background-size: 100% auto;
  position: relative;
  margin-bottom: .3rem;
  z-index: 10;
}

.boxall:before,
.boxall:after {
  position: absolute;
  width: .1rem;
  height: .1rem;
  content: "";
  border-top: 2px solid #02a6b5;
  top: 0;
}

.boxall:before,
.boxfoot:before {
  border-left: 2px solid #02a6b5;
  left: 0;
}

.boxall:after,
.boxfoot:after {
  border-right: 2px solid #02a6b5;
  right: 0;
}

.alltitle {
  font-size: .24rem;
  color: #fff;
  text-align: center;
  line-height: .6rem;
  border-bottom: 1px solid rgba(255, 255, 255, .2);
  position: relative;
  z-index: 20;
}

.sycm {
  position: relative;
  z-index: 15;
}

.sycm ul {
  margin-left: -.5rem;
  margin-right: -.5rem;
  padding: .16rem 0;
  display: block;
}

.sycm li {
  float: left;
  width: 33.33%;
  text-align: center;
  position: relative;
  display: block;
}

.sycm li:before {
  position: absolute;
  content: "";
  height: 30%;
  width: 1px;
  background: rgba(255, 255, 255, .1);
  right: 0;
  top: 15%;
}

.sycm li:last-child:before {
  width: 0;
}

.sycm li h2 {
  font-size: .3rem;
  color: #c5ccff;
  display: block;
}

.sycm li span {
  font-size: .18rem;
  color: #fff;
  opacity: .5;
  display: block;
}

.boxfoot {
  position: absolute;
  bottom: 0;
  width: 100%;
  left: 0;
}

.boxfoot:before,
.boxfoot:after {
  position: absolute;
  width: .1rem;
  height: .1rem;
  content: "";
  border-bottom: 2px solid #02a6b5;
  bottom: 0;
}

.bar {
  background: rgba(101, 132, 226, .1);
  padding: .15rem;
  z-index: 20;
  position: relative;
}

.barbox li,
.barbox2 li {
  width: 50%;
  text-align: center;
  position: relative;
}

.barbox:before,
.barbox:after {
  position: absolute;
  width: .3rem;
  height: .1rem;
  content: "";
}

.barbox:before {
  border-left: 2px solid #02a6b5;
  left: 0;
  border-top: 2px solid #02a6b5;
}

.barbox:after {
  border-right: 2px solid #02a6b5;
  right: 0;
  bottom: 0;
  border-bottom: 2px solid #02a6b5;
}

.barbox li:first-child:before {
  position: absolute;
  content: "";
  height: 50%;
  width: 1px;
  background: rgba(255, 255, 255, .2);
  right: 0;
  top: 25%;
}

.barbox {
  border: 1px solid rgba(25, 186, 139, .17);
  position: relative;
}

.barbox li {
  font-size: .6rem;
  color: #ffeb7b;
  padding: .05rem 0;
  font-family: Gotham, "Helvetica Neue", Helvetica, Arial, "sans-serif";
  font-weight: bold;
}

.barbox2 li {
  font-size: .19rem;
  color: #637c9f;
  padding-top: .1rem;
}

.map {
  position: relative;
  height: 7.2rem;
  z-index: 9;
}

.map4 {
  width: 200%;
  height: 7rem;
  position: relative;
  left: -50%;
  top: 4%;
  margin-top: .2rem;
  z-index: 5;
}

.map1,
.map2,
.map3 {
  position: absolute;
}

.map1 {
  width: 6.43rem;
  z-index: 2;
  top: .45rem;
  left: .4rem;
  animation: myfirst2 15s infinite linear;
}

.map2 {
  width: 5.66rem;
  top: .85rem;
  left: .77rem;
  z-index: 3;
  opacity: 0.2;
  animation: myfirst 10s infinite linear;
}

.map3 {
  width: 5.18rem;
  top: 1.07rem;
  left: 1.13rem;
  z-index: 1;
}

#echarts1,
#echarts2,
#echarts3,
#echarts6,
#echarts7,
#echarts8 {
  position: relative;
}

#echarts1:before,
#echarts2:before,
#echarts3:before,
#echarts6:before,
#echarts7:before,
#echarts8:before {
  position: absolute;
  content: "23124";
  width: 100%;
  text-align: center;
  bottom: .15rem;
  color: #fff;
  opacity: .7;
  font-size: .18rem;
}

#echarts1:before {
  content: "总收入"
}

#echarts2:before {
  content: "总费用"
}

#echarts3:before {
  content: "总毛利"
}

#echarts6:before {
  content: "库存车辆"
}

#echarts7:before {
  content: "库存成本"
}

#echarts8:before {
  content: "库存系数"
}



.tabs {
  text-align: center;
  padding: .1rem 0 0 0;
}

.tabs a {
  position: relative;
  display: inline-block;
  margin-left: 1px;
  padding: .05rem .2rem;
  color: #898989;
  transition: all .3s ease-out 0s;
  font-size: 14px;
}

.tabs li {
  display: inline-block;
}

.tabs a:after {
  position: absolute;
  width: 1px;
  height: 10px;
  background-color: rgba(255, 255, 255, .1);
  content: '';
  margin-left: 0;
  right: -1px;
  margin-top: 7px;


}

.tabs li a.active {
  border: 1px solid rgba(25, 186, 139, .17);
  background: rgba(255, 255, 255, .05);
  color: #fff;
}

.tit02 {
  text-align: center;
  margin: .1rem 0;
  position: relative
}

.tit02 span {
  border: 1px solid rgba(25, 186, 139, .17);
  letter-spacing: 2px;
  padding: .01rem .2rem;
  background: rgba(255, 255, 255, .05);
  font-size: .18rem;
  color: #49bcf7;
}

.tit02:before,
.tit02:after {
  position: absolute;
  width: 32%;
  height: 1px;
  background: rgba(25, 186, 139, .2);
  content: "";
  top: .12rem;
}

.tit02:after {
  right: 0;
}

.tit02:before {
  left: 0;
}

.wrap {
  height: 2.54rem;
  overflow: hidden;
}

.wrap li {
  line-height: .42rem;
  height: .42rem;
  font-size: .18rem;
  text-indent: .24rem;
  margin-bottom: .1rem;
}

.wrap li p {
  border: 1px solid rgba(25, 186, 139, .17);
  color: rgba(255, 255, 255, .6);
}

.sy {
  float: left;
  width: 33%;
  height: 2.2rem;
  margin-top: -.25rem;
}


.adduser {
  height: 1.5rem;
  overflow: hidden;
}

.adduser li {
  height: .5rem;
}

.adduser img {
  width: .40rem;
  border-radius: .5rem;
  margin-right: .1rem;
  display: inline-block;
  vertical-align: middle;
}

.adduser span {
  line-height: .5rem;
  font-size: .18rem;
  color: rgba(255, 255, 255, .6);
}

.sycm ul {
  margin-left: -.5rem;
  margin-right: -.5rem;
  padding: .16rem 0;
}

.sycm li {
  float: left;
  width: 33.33%;
  text-align: center;
  position: relative
}

.sycm li:before {
  position: absolute;
  content: "";
  height: 30%;
  width: 1px;
  background: rgba(255, 255, 255, .1);
  right: 0;
  top: 15%;
}

.sycm li:last-child:before {
  width: 0;
}

.sycm li h2 {
  font-size: .3rem;
  color: #c5ccff;
}

.sycm li span {
  font-size: .18rem;
  color: #fff;
  opacity: .5;
}

@keyframes myfirst2 {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

@keyframes myfirst {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(-359deg);
  }
}