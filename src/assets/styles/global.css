/* 全局变量 */
:root {
  /* 主题色 */
  --primary-color: #18a058;
  --primary-hover: #36ad6a;
  --primary-active: #0c7a43;
  --primary-bg: #e6f7e6;

  /* 中性色 */
  --border-color: #dcdfe6;
  --text-color: #606266;

  /* 圆角 */
  --border-radius: 4px;
}

/* Naive UI 组件全局样式覆盖 */
.n-radio-group {
  display: flex;
  gap: 8px;
}

/* 隐藏 radio-group 的分隔线 */
.n-radio-group__splitor {
  display: none !important;
}

.n-radio {
  display: none !important;
}

.n-radio-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 0 16px;
  font-size: 14px;
  border: 1px solid var(--border-color);
  border-radius: 0 !important;
  color: var(--text-color);
  background: #fff;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  margin-right: -1px;
  text-align: center;
}

.n-radio-button::after {
  display: none !important;
}

.n-radio-group .n-radio-button:first-child {
  border-radius: 0 !important;
  border-left: 1px solid var(--border-color) !important;
}

.n-radio-group .n-radio-button:not(:first-child):not(:last-child) {
  border-radius: 0 !important;
}

.n-radio-group .n-radio-button:last-child {
  border-radius: 0 !important;
  margin-right: 0;
  /* 最后一个按钮不需要负边距，确保右边框可见 */
  border-right: 1px solid var(--border-color) !important;
}

.n-radio-button:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
  z-index: 10;
  /* 提高z-index确保hover时边框完全可见 */
}

/* 自定义单选按钮组样式 - 针对使用 custom-radio-group 类的组件 */
.custom-radio-group .n-radio-button {
  border: none !important;
  background: transparent;
  transition: all 0.2s;
}

/* 修复第一个按钮的左边框问题 */
.custom-radio-group .n-radio-button:first-child {
  border-left: none !important;
}

/* 修复最后一个按钮的右边框问题 */
.custom-radio-group .n-radio-button:last-child {
  border-right: none !important;
}

.custom-radio-group .n-radio-button:hover {
  color: var(--primary-color);
  background-color: rgba(24, 160, 88, 0.1);
  border: none !important;
}

/* 修复第一个按钮悬停时的左边框问题 */
.custom-radio-group .n-radio-button:first-child:hover:not(.n-radio-button--checked) {
  border-left: none !important;
}

/* 修复最后一个按钮悬停时的右边框问题 */
.custom-radio-group .n-radio-button:last-child:hover:not(.n-radio-button--checked) {
  border-right: none !important;
}

.custom-radio-group .n-radio-button--checked {
  color: #fff !important;
  background-color: var(--primary-color) !important;
  border: none !important;
}

/* 保留原有的默认样式，用于不使用 custom-radio-group 类的组件 */
/* 修复第一个按钮hover时左边框问题 - 仅在非选中状态下应用 */
.n-radio-group:not(.custom-radio-group) .n-radio-button:first-child:hover:not(.n-radio-button--checked) {
  border-left: 1px solid var(--primary-color) !important;
}

/* 修复最后一个按钮hover时右边框问题 - 仅在非选中状态下应用 */
.n-radio-group:not(.custom-radio-group) .n-radio-button:last-child:hover:not(.n-radio-button--checked) {
  border-right: 1px solid var(--primary-color) !important;
}

.n-radio-button--checked {
  color: #fff !important;
  background-color: var(--primary-color) !important;
  border: none !important;
  z-index: 2;
}

.n-radio-button--checked:hover {
  background-color: var(--primary-hover) !important;
  border: none !important;
}



.n-radio-button--disabled {
  cursor: not-allowed;
  color: #c0c4cc !important;
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
}

.n-radio-button--checked.n-radio-button--disabled {
  color: #fff !important;
  background-color: #a0cfb4 !important;
  border-color: #a0cfb4 !important;
}

/* 数字选择器样式 */
.n-input-number {
  width: 100%;
}

.n-input-number .n-input__input {
  text-align: center !important;
}

.n-input-number-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 100%;
  color: var(--text-color);
  background-color: #f5f7fa;
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.3s;
}

.n-input-number-button:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.n-input-number-button:active {
  background-color: var(--primary-bg);
}

.n-input-number-button--disabled {
  cursor: not-allowed;
  color: #c0c4cc;
  background-color: #f5f7fa;
  border-color: #e4e7ed;
}

.n-input-number .n-input-wrapper {
  display: flex !important;
}

.n-input-number .n-input__prefix,
.n-input-number .n-input__suffix {
  margin: 0 !important;
  padding: 0 !important;
}

/* 基础样式 */
body {
  margin: 0;
  padding: 0;
  /* 移除字体定义，使用App.vue中的全局字体定义 */
}

.no-scroll {
  overflow: hidden;
  height: 100vh;
}

/* 确保按钮内的所有内容居中 */
.n-radio-button>* {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

/* 覆盖n-layout-scroll-container的样式，禁用垂直滚动 */
.n-layout-scroll-container {
  overflow-y: hidden !important;
}

/* 优化滚动性能，减少被动事件监听器警告 */
* {
  touch-action: manipulation;
}

/* 针对数据表格的滚动优化 */
.n-data-table-base-table-body,
.n-data-table-wrapper,
.data-table-wrapper {
  touch-action: pan-y pan-x;
  will-change: scroll-position;
}

/* 确保虚拟滚动容器的滚动性能 */
.n-virtual-list-container {
  touch-action: pan-y;
  will-change: scroll-position;
}

/* ========== 系统级滚动条样式设置 ========== */
/* 全局滚动条样式 - 1.5px 细滚动条设计 */
::-webkit-scrollbar {
  width: 1.5px;
  /* 垂直滚动条宽度 */
  height: 1px;
  /* 水平滚动条高度 */
}

::-webkit-scrollbar-track {
  background: transparent;
  /* 透明轨道，更简洁 */
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 1px;
  /* 细圆角 */
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
  /* 悬停时稍微深一点 */
}

::-webkit-scrollbar-corner {
  background: transparent;
  /* 滚动条交汇处透明 */
}

/* 针对不同容器的滚动条样式统一 */
.n-scrollbar::-webkit-scrollbar,
.n-data-table-wrapper::-webkit-scrollbar,
.n-layout-scroll-container::-webkit-scrollbar,
.data-table-wrapper::-webkit-scrollbar {
  width: 1.5px;
  height: 1px;
}

.n-scrollbar::-webkit-scrollbar-track,
.n-data-table-wrapper::-webkit-scrollbar-track,
.n-layout-scroll-container::-webkit-scrollbar-track,
.data-table-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

.n-scrollbar::-webkit-scrollbar-thumb,
.n-data-table-wrapper::-webkit-scrollbar-thumb,
.n-layout-scroll-container::-webkit-scrollbar-thumb,
.data-table-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 1px;
}

.n-scrollbar::-webkit-scrollbar-thumb:hover,
.n-data-table-wrapper::-webkit-scrollbar-thumb:hover,
.n-layout-scroll-container::-webkit-scrollbar-thumb:hover,
.data-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}