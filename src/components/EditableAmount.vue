<template>
  <div
    :style="disabled
      ? 'min-height: 22px'
      : 'min-height: 22px; cursor: pointer; text-decoration: underline dotted; color: #18a058;'"
    @click="handleOnClick"
  >
    <n-input-number
      v-if="isEdit"
      ref="inputRef"
      :value="parseFloat(inputValue)"
      @update:value="handleInputChange"
      @blur="handleBlur"
      :min="0.01"
      :precision="2"
      style="width: 160px;"
      button-placement="both"
    />
    <span v-else>{{ formattedAmount }}</span>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { NInputNumber } from "naive-ui";

const props = defineProps({
  value: {
    type: Number,
    default: 0
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:value']);

const isEdit = ref(false);
const inputRef = ref(null);

// 使用计算属性来动态获取当前值，确保每次渲染时都使用最新的props.value
const currentValue = computed(() => props.value || 0);

// 将分转换为元的输入值
const inputValue = ref((currentValue.value / 100).toFixed(2));

// 当props.value变化时，更新inputValue和显示值
watch(
  () => props.value,
  (newValue) => {
    if (!isEdit.value) {
      // 只在非编辑状态下更新，避免编辑时被覆盖
      inputValue.value = ((newValue || 0) / 100).toFixed(2);
    }
  },
  { immediate: true }
); // 立即执行一次

function handleOnClick() {
  if (props.disabled) return;
  // 在进入编辑模式前，确保inputValue是最新的
  inputValue.value = (currentValue.value / 100).toFixed(2);
  isEdit.value = true;
  nextTick(() => {
    inputRef.value?.focus();
  });
}

// 处理输入框值变化 - 只更新本地状态，不发送请求
function handleInputChange(v) {
  inputValue.value = v !== null ? v.toString() : "0";
}

// 处理失去焦点 - 验证并发送请求
function handleBlur() {
  // 检查金额是否大于0
  const amount = parseFloat(inputValue.value);
  if (isNaN(amount) || amount <= 0) {
    // 如果金额无效，恢复原值
    inputValue.value = (currentValue.value / 100).toFixed(2);
    isEdit.value = false;
    return;
  }

  // 将元转换为分
  const amountInCents = Math.round(amount * 100);

  // 检查金额是否发生变化
  if (amountInCents !== currentValue.value) {
    // 只有金额发生变化时才调用更新函数
    emit('update:value', amountInCents);
  }

  isEdit.value = false;
}

// 格式化显示金额的计算属性，确保每次渲染时都使用最新值
const formattedAmount = computed(() => {
  const valueInYuan = (currentValue.value / 100).toFixed(2);
  return `¥${Number(valueInYuan).toLocaleString()}`;
});
</script>
