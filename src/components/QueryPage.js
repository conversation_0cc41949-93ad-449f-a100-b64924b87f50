import { ref, reactive, computed, onMounted, onUnmounted, h } from "vue";
import { NTag } from "naive-ui";
import {
  ChevronDown,
  ChevronUp,
  RefreshOutline,
  AddOutline,
  SearchOutline,
} from "@vicons/ionicons5";
import messages from "@/utils/messages";
import { getDictOptions } from "@/api/dict";

/**
 * QueryPage 组合式函数
 * 提供查询页面的所有逻辑功能
 */
export default function useQueryPage(props, emit) {
  // 响应式数据
  const tableRef = ref(null);
  const filterContent = ref(null);
  const loading = ref(false);
  const tableData = ref([]);
  const selectedRows = ref([]);
  const isFilterCollapsed = ref(false);
  const windowHeight = ref(window.innerHeight);

  // 筛选表单
  const filterForm = reactive({});

  // 字典数据缓存
  const dictCache = reactive({});

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 50,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    itemCount: 0,
    showQuickJumper: false,
  });

  // 格式化函数
  const formatNumber = (num, decimals = 2) => {
    if (num === null || num === undefined || num === '') return '-';
    const number = typeof num === 'string' ? parseFloat(num) : num;
    if (isNaN(number)) return '-';
    return number.toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    });
  };

  const formatCurrency = (amount, currency = '¥', decimals = 2) => {
    if (amount === null || amount === undefined || amount === '') return '-';
    const number = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(number)) return '-';
    return `${currency}${formatNumber(number, decimals)}`;
  };

  // 计算属性
  const hasFilters = computed(() => {
    return props.fields.some((field) => field.filter);
  });

  const visibleFilterFields = computed(() => {
    const filterFields = props.fields.filter((field) => field.filter);
    if (isFilterCollapsed.value) {
      return filterFields.slice(0, 3); // 收起时只显示前3个
    }
    return filterFields;
  });

  const tableMaxHeight = computed(() => {
    const pagepadding = 32;
    const filterHeight = hasFilters.value ? 120 : 0;
    const toolbarHeight = 60;
    const paginationHeight = 50;
    const margin = 16;

    const calculatedHeight =
      windowHeight.value -
      pagepadding -
      filterHeight -
      toolbarHeight -
      paginationHeight -
      margin;

    let maxHeight = calculatedHeight;
    if (calculatedHeight > 800) {
      maxHeight = Math.max(calculatedHeight * 0.85, 600);
    } else if (calculatedHeight > 600) {
      maxHeight = calculatedHeight;
    } else {
      maxHeight = Math.max(calculatedHeight, 400);
    }

    return Math.max(300, maxHeight);
  });

  const computedColumns = computed(() => {
    const cols = [];

    // 添加选择列
    if (props.config.selection !== false) {
      cols.push({
        type: "selection",
        width: 50,
        fixed: "left" // 选择列固定在左侧
      });
    }

    // 添加数据列
    props.fields.forEach((field) => {
      if (field.table !== false) {
        const column = {
          title: field.label || field.name,
          key: field.name,
          width: field.width,
          fixed: field.frozen ? "left" : field.fixed, // 支持冻结列
          align: field.align || "center",
          sorter: field.sortable ? "default" : false,
          ellipsis: field.ellipsis !== false ? { tooltip: true } : false,
        };

        // 根据字段类型设置渲染函数
        if (field.render) {
          // 自定义渲染函数优先级最高
          column.render = field.render;
        } else {
          column.render = (row) => {
            const value = row[field.name];

            switch (field.type) {
              case 'number':
                return h("span", {
                  style: { fontFamily: 'Monaco, "Courier New", monospace' }
                }, formatNumber(value, field.decimals || 0));

              case 'currency':
                return h("span", {
                  style: { fontWeight: 'bold', color: '#18a058' }
                }, formatCurrency(value, field.currency || '¥', field.decimals || 2));

              case 'dict':
                return renderDictField(field, value);

              default:
                return h("span", null, value || '-');
            }
          };
        }

        cols.push(column);
      }
    });

    // 添加操作列
    if (props.config.actions !== false) {
      cols.push({
        title: "操作",
        key: "actions",
        width: props.config.actionWidth || 120,
        fixed: "right",
        align: "center",
        render: (row) => {
          const actions = [];

          // 查看按钮
          if (props.config.actions?.view !== false) {
            actions.push(
              h(
                "div",
                {
                  style: {
                    cursor: "pointer",
                    color: "#2080f0",
                    fontSize: "16px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: "4px",
                  },
                  onClick: () => emit("view", row),
                  title: "查看",
                },
                ["👁"]
              )
            );
          }

          // 编辑按钮
          if (props.config.actions?.edit !== false) {
            actions.push(
              h(
                "div",
                {
                  style: {
                    cursor: "pointer",
                    color: "#18a058",
                    fontSize: "16px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: "4px",
                  },
                  onClick: () => emit("edit", row),
                  title: "编辑",
                },
                ["✏️"]
              )
            );
          }

          // 删除按钮
          if (props.config.actions?.delete !== false) {
            actions.push(
              h(
                "div",
                {
                  style: {
                    cursor: "pointer",
                    color: "#d03050",
                    fontSize: "16px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: "4px",
                  },
                  onClick: () => emit("delete", row),
                  title: "删除",
                },
                ["🗑"]
              )
            );
          }

          return h(
            "div",
            {
              style: {
                display: "flex",
                justifyContent: "center",
                gap: "8px",
              },
            },
            actions
          );
        },
      });
    }

    return cols;
  });

  // 日期快捷选项
  const dateShortcuts = {
    今日: () => {
      const end = new Date();
      const start = new Date(end.getFullYear(), end.getMonth(), end.getDate());
      return [start, end];
    },
    昨日: () => {
      const end = new Date();
      end.setTime(end.getTime() - 3600 * 1000 * 24);
      const start = new Date(end.getFullYear(), end.getMonth(), end.getDate());
      return [start, end];
    },
    最近7天: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
    最近30天: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  };

  // 字典相关方法
  const loadDictData = async (dictKey) => {
    if (dictCache[dictKey]) {
      return dictCache[dictKey];
    }

    try {
      const response = await getDictOptions(dictKey);
      if (response && response.code === 200 && response.data) {
        const options = response.data.map(item => ({
          label: item.optionLabel,
          value: item.optionValue,
          color: item.color || undefined, // 支持字典项颜色
          type: item.type || 'default' // 支持字典项类型
        }));
        dictCache[dictKey] = options;
        return options;
      }
    } catch (error) {
      console.error(`加载字典数据失败: ${dictKey}`, error);
    }

    return [];
  };

  const renderDictField = (field, value) => {
    if (value === null || value === undefined || value === '') {
      return h("span", null, '-');
    }

    // 如果字段有dictKey，从缓存中获取字典数据
    let options = [];
    if (field.dictKey && dictCache[field.dictKey]) {
      options = dictCache[field.dictKey];
    } else if (field.options) {
      options = field.options;
    }

    // 支持多值字段（逗号分隔）
    const values = Array.isArray(value) ? value : String(value).split(',').filter(v => v.trim());

    if (values.length === 0) {
      return h("span", null, '-');
    }

    // 如果是单值且不渲染为tag
    if (values.length === 1 && !field.renderAsTag) {
      const option = options.find(opt => String(opt.value) === String(values[0]));
      return h("span", null, option?.label || values[0]);
    }

    // 渲染为tag形式
    return h("div", {
      style: {
        display: 'flex',
        flexWrap: 'wrap',
        gap: '4px',
        justifyContent: 'center'
      }
    }, values.map(val => {
      const option = options.find(opt => String(opt.value) === String(val));
      return h(NTag, {
        key: val,
        type: option?.type || 'default',
        size: 'small',
        style: option?.color ? { backgroundColor: option.color } : undefined
      }, () => option?.label || val);
    }));
  };

  // 方法定义
  const initializeFilterForm = () => {
    props.fields.forEach((field) => {
      if (field.filter) {
        if (field.type === "date" || field.type === "datetime") {
          filterForm[`${field.name}Range`] = null;
        } else {
          filterForm[field.name] = field.defaultValue || null;
        }
      }
    });
    // 添加关键词搜索
    if (props.config.search !== false) {
      filterForm.keywords = "";
    }
  };

  const getDictOptions = (field) => {
    // 优先使用字段自定义的options
    if (field.options && field.options.length > 0) {
      return field.options;
    }

    // 如果有dictKey，从缓存中获取字典数据
    if (field.dictKey && dictCache[field.dictKey]) {
      return dictCache[field.dictKey];
    }

    return [];
  };

  const getColSpan = (field) => {
    // 响应式布局：小于1080px时每个字段占一行，大于1080px时每行3个字段
    if (window.innerWidth < 1080) {
      return 24;
    }
    return field.span || 8;
  };

  const updateFilterValue = (fieldName, value) => {
    filterForm[fieldName] = value;
  };

  const toggleFilterCollapse = () => {
    isFilterCollapsed.value = !isFilterCollapsed.value;
  };

  const handleFilterChange = () => {
    // 立即刷新数据
    handleFilter();
  };

  const handleFilter = () => {
    pagination.page = 1;
    refreshData();
  };

  const resetFilter = () => {
    props.fields.forEach((field) => {
      if (field.filter) {
        if (field.type === "date" || field.type === "datetime") {
          filterForm[`${field.name}Range`] = null;
        } else {
          filterForm[field.name] = field.defaultValue || null;
        }
      }
    });
    if (props.config.search !== false) {
      filterForm.keywords = "";
    }
    handleFilter();
  };

  const handleClear = () => {
    filterForm.keywords = "";
    handleFilter();
  };

  const refreshData = async () => {
    loading.value = true;
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: pagination.pageSize,
      };

      // 添加筛选条件
      props.fields.forEach((field) => {
        if (
          field.filter &&
          filterForm[field.name] !== null &&
          filterForm[field.name] !== ""
        ) {
          if (field.type === "date" || field.type === "datetime") {
            const range = filterForm[`${field.name}Range`];
            if (range && range.length === 2) {
              params[`${field.name}Start`] = range[0];
              params[`${field.name}End`] = range[1];
            }
          } else {
            params[field.name] = filterForm[field.name];
          }
        }
      });

      // 添加关键词搜索
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords;
      }

      // 调用API
      let response;
      if (props.apiService.getList) {
        response = await props.apiService.getList(params);
      } else if (props.apiService[`get${props.config.entity}List`]) {
        response = await props.apiService[`get${props.config.entity}List`](
          params
        );
      } else {
        throw new Error("API服务未提供有效的获取列表方法");
      }

      if (response.code === 200) {
        tableData.value = response.data.list || response.data || [];
        pagination.itemCount = response.data.total || tableData.value.length;

        // 更新分页信息
        if (response.data.pageNum) {
          pagination.page = response.data.pageNum;
        }

        emit("data-loaded", tableData.value);
      } else {
        messages.error(response.message || "数据加载失败");
      }
    } catch (error) {
      console.error("加载数据失败:", error);
      messages.error("加载数据失败，请稍后重试");
    } finally {
      loading.value = false;
    }
  };

  const handleAdd = () => {
    emit("add");
  };

  const handleCustomAction = (button) => {
    emit("custom-action", button, selectedRows.value);
  };

  const handleSelectionChange = (keys) => {
    selectedRows.value = tableData.value.filter((item) =>
      keys.includes(item[props.config.rowKey || "id"])
    );
    emit("selection-change", selectedRows.value);
  };

  const handlePageChange = (page) => {
    pagination.page = page;
    refreshData();
  };

  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    refreshData();
  };

  // 窗口大小变化监听器
  const handleResize = () => {
    windowHeight.value = window.innerHeight;
  };

  // 初始化字典数据
  const initializeDictData = async () => {
    const dictFields = props.fields.filter(field => field.dictKey);
    const loadPromises = dictFields.map(field => loadDictData(field.dictKey));

    try {
      await Promise.all(loadPromises);
    } catch (error) {
      console.error('初始化字典数据失败:', error);
    }
  };

  // 生命周期
  onMounted(async () => {
    initializeFilterForm();
    await initializeDictData(); // 先加载字典数据
    refreshData();
    window.addEventListener("resize", handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener("resize", handleResize);
  });

  // 返回所有需要的数据和方法
  return {
    // 图标
    ChevronDown,
    ChevronUp,
    RefreshOutline,
    AddOutline,
    SearchOutline,

    // 响应式数据
    tableRef,
    filterContent,
    loading,
    tableData,
    selectedRows,
    isFilterCollapsed,
    windowHeight,
    filterForm,
    pagination,

    // 计算属性
    hasFilters,
    visibleFilterFields,
    tableMaxHeight,
    computedColumns,

    // 工具数据
    dateShortcuts,

    // 格式化函数
    formatNumber,
    formatCurrency,

    // 方法
    initializeFilterForm,
    getDictOptions,
    getColSpan,
    updateFilterValue,
    toggleFilterCollapse,
    handleFilterChange,
    handleFilter,
    resetFilter,
    handleClear,
    refreshData,
    handleAdd,
    handleCustomAction,
    handleSelectionChange,
    handlePageChange,
    handlePageSizeChange,
    handleResize,
    loadDictData,
    renderDictField,

    // 暴露给父组件的方法
    getSelectedRows: () => selectedRows.value,
    getFilterForm: () => filterForm,
    getDictCache: () => dictCache,
  };
}
