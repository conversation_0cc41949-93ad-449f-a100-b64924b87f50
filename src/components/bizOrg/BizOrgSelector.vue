<template>
  <n-modal
    :show="modelVisible"
    @update:show="updateVisible"
    :title="title || '业务机构选择'"
    preset="card"
    :style="{ width: '1000px', maxWidth: '90%' }"
    :mask-closable="false"
    :auto-focus="false"
    class="biz-org-selector-modal"
  >
    <div class="biz-org-selector-content">
      <!-- 筛选区域 -->
      <div class="filter-area">
        <n-space :size="12" :wrap="false" align="center">
          <!-- 省/直辖市 -->
          <n-form-item label="省/直辖市" style="margin-bottom: 0">
            <n-select
              v-model:value="filters.province"
              :options="provinceOptions"
              placeholder="请选择省份"
              clearable
              style="width: 140px"
              @update:value="handleProvinceChange"
            />
          </n-form-item>

          <!-- 市/区 -->
          <n-form-item label="市/区" style="margin-bottom: 0">
            <n-select
              v-model:value="filters.city"
              :options="cityOptions"
              placeholder="请选择城市"
              clearable
              style="width: 140px"
              :disabled="!filters.province"
            />
          </n-form-item>

          <!-- 品牌 -->
          <n-form-item label="品牌" style="margin-bottom: 0">
            <n-select
              v-model:value="filters.mainBrand"
              :options="brandOptions"
              placeholder="请选择品牌"
              clearable
              style="width: 140px"
            />
          </n-form-item>

          <!-- 业务权限 -->
          <n-form-item label="业务权限" style="margin-bottom: 0">
            <n-select
              v-model:value="filters.businessPermission"
              :options="businessPermissionOptions"
              placeholder="请选择权限"
              clearable
              style="width: 140px"
            />
          </n-form-item>

          <!-- 关键字搜索 -->
          <n-form-item label="关键字" style="margin-bottom: 0">
            <n-input
              v-model:value="searchKeyword"
              placeholder="请输入机构名称或地址"
              clearable
              style="width: 200px"
            >
              <template #prefix>
                <n-icon>
                  <component :is="SearchOutlineIcon" />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>
        </n-space>
      </div>

      <!-- 数据列表 -->
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="filteredBizOrgData"
        :loading="loading"
        :row-key="(row) => row.id"
        :row-props="rowProps"
        :checked-row-keys="selectedBizOrgIds"
        @update:checked-row-keys="handleSelectionChange"
        :max-height="400"
        :scroll-x="800"
      />
    </div>
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :disabled="selectedBizOrgIds.length === 0"
          @click="handleConfirm"
          >确定</n-button
        >
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, h, markRaw, watch } from "vue";
import {
  NModal,
  NInput,
  NButton,
  NSpace,
  NDataTable,
  NIcon,
  NTag,
  NSelect,
  NFormItem,
} from "naive-ui";
import { SearchOutline } from "@vicons/ionicons5";
import { getBizOrgList } from "@/api/bizOrg";
import { getDictOptions } from "@/api/dict";
import { vehicleBrandUtils } from "@/utils/dictUtils";
import messages from "@/utils/messages";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const SearchOutlineIcon = markRaw(SearchOutline);

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "业务机构选择",
  },
  // 筛选条件props
  province: {
    type: String,
    default: null,
  },
  city: {
    type: String,
    default: null,
  },
  brand: {
    type: String,
    default: null,
  },
  businessPermission: {
    type: String,
    default: null,
  },
  // 可选的初始选中机构
  initialBizOrg: {
    type: Object,
    default: null,
  },
  // 已选择的机构列表（多选模式）
  selectedOrgs: {
    type: Array,
    default: () => [],
  },
  // 是否单选模式
  single: {
    type: Boolean,
    default: false,
  },
  // 是否查询全部数据
  listAll: {
    type: Boolean,
    default: false,
  },
});

// 定义组件事件
const emit = defineEmits(["update:visible", "select", "cancel"]);

// 组件状态
const loading = ref(false);
const searchKeyword = ref("");
const bizOrgData = ref([]);
const selectedBizOrgIds = ref([]);
const selectedBizOrgs = ref([]);
const tableRef = ref(null);

// 筛选条件
const filters = reactive({
  province: null,
  city: null,
  mainBrand: null,
  businessPermission: null,
});

// 字典选项数据
const provinceOptions = ref([]);
const cityOptions = ref([]);
const brandOptions = ref([]);
const businessPermissionOptions = ref([]);

// 字典映射表，用于快速查找字典标签
const dictMaps = reactive({
  province: {},
  city: {},
  brand: {},
  businessPermission: {},
});

// 表格列配置
const columns = [
  {
    type: "selection",
    disabled: () => false,
    multiple: computed(() => !props.single),
  },
  { title: "机构名称", key: "orgName", width: 200 },
  {
    title: "省/直辖市",
    key: "province",
    width: 100,
    render(row) {
      return dictMaps.province[row.province] || row.province || "-";
    },
  },
  {
    title: "市/区",
    key: "city",
    width: 100,
    render(row) {
      return dictMaps.city[row.city] || row.city || "-";
    },
  },
  {
    title: "主营品牌",
    key: "salesBrands",
    width: 150,
    render(row) {
      if (!row.salesBrands) return "-";

      const brands = row.salesBrands.split(",").map((brand) => brand.trim());
      return h(
        "div",
        {
          style: { display: "flex", gap: "4px", flexWrap: "wrap" },
        },
        brands.map((brand) => {
          // 处理品牌显示：如果是中文名称直接使用，如果是代码则转换为中文
          let brandLabel = brand;
          let brandColor = "#909399";
          let brandType = "default";

          // 尝试通过品牌代码获取信息（如果brand是代码）
          const labelFromCode = vehicleBrandUtils.getLabel(brand);
          if (labelFromCode !== "未知") {
            // brand是有效的品牌代码
            brandLabel = labelFromCode;
            brandColor = vehicleBrandUtils.getColor(brand);
            brandType = vehicleBrandUtils.getType(brand);
          } else {
            // brand可能是中文名称，尝试通过中文名称获取代码
            const brandCode = vehicleBrandUtils.getBrandValueByLabel(brand);
            if (brandCode) {
              // 找到了对应的品牌代码，使用代码获取样式信息
              brandColor = vehicleBrandUtils.getColor(brandCode);
              brandType = vehicleBrandUtils.getType(brandCode);
            }
            // brandLabel保持为中文名称
          }

          return h(
            NTag,
            {
              type: brandType,
              size: "small",
              bordered: false,
              style: {
                color: brandColor,
                fontWeight: "bold",
              },
            },
            { default: () => brandLabel }
          );
        })
      );
    },
  },
  {
    title: "业务权限",
    key: "bizPermissions",
    width: 200,
    render(row) {
      if (!row.bizPermissions) return "-";

      const permissions = row.bizPermissions
        .split(",")
        .map((permission) => permission.trim());
      return h(
        "div",
        {
          style: { display: "flex", gap: "4px", flexWrap: "wrap" },
        },
        permissions.map((permission) => {
          const permissionLabel =
            dictMaps.businessPermission[permission] || permission;
          return h(
            NTag,
            {
              type: "success",
              size: "small",
              bordered: false,
            },
            { default: () => permissionLabel }
          );
        })
      );
    },
  },
];

// 监听品牌属性变化
watch(
  () => props.brand,
  (newVal) => {
    filters.mainBrand = newVal;
  },
  { immediate: true }
);

// 计算属性：本地过滤后的数据
const filteredBizOrgData = computed(() => {
  let filtered = bizOrgData.value;

  // 省份筛选
  if (filters.province) {
    filtered = filtered.filter((org) => org.province === filters.province);
  }

  // 城市筛选
  if (filters.city) {
    filtered = filtered.filter((org) => org.city === filters.city);
  }

  // 品牌筛选 - 使用vehicleBrandUtils进行匹配
  if (filters.mainBrand) {
    const brandValue = filters.mainBrand.trim();
    const brandLabel = vehicleBrandUtils.getLabel(brandValue); // 获取品牌代码对应的中文名称

    filtered = filtered.filter((org) => {
      if (!org.salesBrands) return false;
      const brands = org.salesBrands.split(",").map((brand) => brand.trim());
      // 同时匹配品牌代码和品牌名称
      return brands.includes(brandValue) || brands.includes(brandLabel);
    });
  }

  // 业务权限筛选
  if (filters.businessPermission) {
    filtered = filtered.filter((org) => {
      if (!org.bizPermissions) return false;
      const permissions = org.bizPermissions.split(",").map((p) => p.trim());
      return permissions.includes(filters.businessPermission.trim());
    });
  }

  // 关键字搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase().trim();
    filtered = filtered.filter((org) => {
      return (
        (org.orgName && org.orgName.toLowerCase().includes(keyword)) ||
        (org.address && org.address.toLowerCase().includes(keyword)) ||
        (org.province &&
          dictMaps.province[org.province] &&
          dictMaps.province[org.province].toLowerCase().includes(keyword)) ||
        (org.city &&
          dictMaps.city[org.city] &&
          dictMaps.city[org.city].toLowerCase().includes(keyword))
      );
    });
  }

  return filtered;
});

// 计算属性：模态框可见性
const modelVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

// 更新可见性
const updateVisible = (val) => {
  emit("update:visible", val);
};

// 监听visible属性变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 弹窗打开时，重置搜索条件
      searchKeyword.value = "";

      // 设置初始筛选条件
      filters.province = props.province;
      filters.city = props.city;
      filters.businessPermission = props.businessPermission;

      // 每次打开弹窗都重新加载数据，确保数据是最新的
      loadDictOptions().then(() => {
        fetchBizOrgs();
      });

      // 如果有初始选中机构，则设置选中状态
      if (props.single && props.initialBizOrg) {
        selectedBizOrgIds.value = [props.initialBizOrg.id];
        selectedBizOrgs.value = [props.initialBizOrg];
      } else if (
        !props.single &&
        props.selectedOrgs &&
        props.selectedOrgs.length > 0
      ) {
        selectedBizOrgIds.value = props.selectedOrgs.map((org) => org.id);
        selectedBizOrgs.value = [...props.selectedOrgs];
      } else {
        selectedBizOrgIds.value = [];
        selectedBizOrgs.value = [];
      }
    }
  }
);

// 加载字典选项数据
const loadDictOptions = async () => {
  try {
    // 加载省份选项
    const provinceRes = await getDictOptions("province_city");
    if (provinceRes.code === 200) {
      provinceOptions.value = provinceRes.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue,
      }));
      // 构建省份字典映射表
      provinceRes.data.forEach((item) => {
        dictMaps.province[item.optionValue] = item.optionLabel;
      });
    }

    // 加载城市选项
    const cityRes = await getDictOptions("city_district");
    if (cityRes.code === 200) {
      // 构建城市字典映射表
      cityRes.data.forEach((item) => {
        dictMaps.city[item.optionValue] = item.optionLabel;
      });
    }

    // 使用vehicleBrandUtils加载品牌选项（应用权限过滤）
    brandOptions.value = await vehicleBrandUtils.getOptionsAsync(true);

    // 构建品牌字典映射表 - 双向映射
    const brandRes = await getDictOptions("vehicle_brand");
    if (brandRes.code === 200) {
      brandRes.data.forEach((item) => {
        // 品牌代码 -> 品牌名称
        dictMaps.brand[item.optionValue] = item.optionLabel;
        // 品牌名称 -> 品牌代码
        dictMaps.brand[item.optionLabel] = item.optionValue;
      });
    }

    // 加载业务权限选项
    const permissionRes = await getDictOptions("business_permission");
    if (permissionRes.code === 200) {
      businessPermissionOptions.value = permissionRes.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue,
      }));
      // 构建业务权限字典映射表
      permissionRes.data.forEach((item) => {
        dictMaps.businessPermission[item.optionValue] = item.optionLabel;
      });
    }
  } catch (error) {
    console.error("加载字典选项失败:", error);
    messages.error("加载字典选项失败，请稍后重试");
  }
};

// 处理省份变化 - 级联更新城市选项
const handleProvinceChange = async (provinceValue) => {
  filters.city = null; // 清空城市选择

  if (!provinceValue) {
    cityOptions.value = [];
    return;
  }

  try {
    const cityRes = await getDictOptions("city_district");
    if (cityRes.code === 200) {
      // 筛选出属于当前省份的城市
      const filteredCities = cityRes.data.filter(
        (item) => item.parent === provinceValue
      );
      cityOptions.value = filteredCities.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue,
      }));
    }
  } catch (error) {
    console.error("加载城市选项失败:", error);
    messages.error("加载城市选项失败，请稍后重试");
  }
};

// 获取业务机构列表（获取全部数据，本地过滤）
const fetchBizOrgs = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {};

    // 如果传入了 listAll 参数且为 true，则添加到查询参数中
    if (props.listAll) {
      params.listAll = true;
    }

    // 调用API获取数据
    const response = await getBizOrgList(params);

    if (response.code === 200) {
      bizOrgData.value = response.data.list || response.data;
    } else {
      messages.error(response.message || "获取业务机构列表失败");
    }
  } catch (error) {
    console.error("获取业务机构列表失败:", error);
    messages.error("获取业务机构列表失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 处理多选变化
const handleSelectionChange = (checkedRowKeys) => {
  if (props.single) {
    // 单选模式：只保留最后选中的一项
    const lastSelectedKey = checkedRowKeys[checkedRowKeys.length - 1];
    selectedBizOrgIds.value = lastSelectedKey ? [lastSelectedKey] : [];
    selectedBizOrgs.value = lastSelectedKey
      ? [bizOrgData.value.find((org) => org.id === lastSelectedKey)]
      : [];
  } else {
    // 多选模式：保持原有逻辑
    selectedBizOrgIds.value = checkedRowKeys;
    selectedBizOrgs.value = bizOrgData.value.filter((org) =>
      checkedRowKeys.includes(org.id)
    );
  }
};

// 处理取消
const handleCancel = () => {
  modelVisible.value = false;
  emit("cancel");
};

// 处理确认
const handleConfirm = () => {
  if (selectedBizOrgs.value.length > 0) {
    // 确保每个机构对象都有orgName字段
    const orgsWithNames = selectedBizOrgs.value.map((org) => {
      // 如果没有orgName字段，但有name字段，则添加orgName字段
      if (!org.orgName && org.name) {
        return { ...org, orgName: org.name };
      }
      // 如果既没有orgName也没有name字段，则添加空字符串
      if (!org.orgName && !org.name) {
        return { ...org, orgName: "", name: "" };
      }
      return org;
    });

    emit("select", orgsWithNames);
    modelVisible.value = false;
  }
};

// 行属性函数 - 添加双击事件和选中样式
const rowProps = (row) => {
  return {
    style: selectedBizOrgIds.value.includes(row.id)
      ? "background-color: rgba(24, 160, 88, 0.1);"
      : "",
    onClick: () => {
      if (props.single) {
        // 单选模式：直接选中当前行，取消其他选中
        selectedBizOrgIds.value = [row.id];
        selectedBizOrgs.value = [row];
      } else {
        // 多选模式：切换选中状态
        const index = selectedBizOrgIds.value.indexOf(row.id);
        if (index > -1) {
          // 取消选中
          selectedBizOrgIds.value.splice(index, 1);
          const orgIndex = selectedBizOrgs.value.findIndex(
            (org) => org.id === row.id
          );
          if (orgIndex > -1) {
            selectedBizOrgs.value.splice(orgIndex, 1);
          }
        } else {
          // 选中
          selectedBizOrgIds.value.push(row.id);
          selectedBizOrgs.value.push(row);
        }
      }
    },
    onDblclick: () => {
      // 双击选中并确认
      if (props.single) {
        // 单选模式：直接选中并确认
        selectedBizOrgIds.value = [row.id];
        // 确保机构对象有orgName字段
        const orgWithName =
          !row.orgName && row.name ? { ...row, orgName: row.name } : row;
        selectedBizOrgs.value = [orgWithName];
      } else if (!selectedBizOrgIds.value.includes(row.id)) {
        // 多选模式：选中并确认
        selectedBizOrgIds.value = [row.id];
        // 确保机构对象有orgName字段
        const orgWithName =
          !row.orgName && row.name ? { ...row, orgName: row.name } : row;
        selectedBizOrgs.value = [orgWithName];
      }
      handleConfirm();
    },
  };
};
</script>

<style scoped>
.biz-org-selector-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-area {
  padding: 12px 0;
  margin-bottom: 12px;
  border-bottom: 1px solid #e0e0e6;
}

:deep(.n-data-table .n-data-table-td) {
  padding: 8px 12px;
}

:deep(.n-tag) {
  margin: 0;
}

/* 选中行样式 */
:deep(.n-data-table-tr.n-data-table-tr--selected) {
  background-color: rgba(24, 160, 88, 0.1) !important;
}

/* 鼠标悬停样式 */
:deep(.n-data-table-tr:hover) {
  cursor: pointer;
  background-color: rgba(24, 160, 88, 0.05);
}

/* 筛选区域样式 */
:deep(.n-form-item .n-form-item-label) {
  font-weight: 500;
  color: #333;
}

:deep(.n-select) {
  min-width: 120px;
}
</style>
