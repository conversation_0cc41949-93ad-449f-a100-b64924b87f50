<template>
  <n-radio-group
    :value="modelValue"
    @update:value="handleUpdate"
    class="custom-radio-group"
  >
    <n-radio-button
      v-for="option in brandOptions"
      :key="option.value"
      :value="option.value"
      class="custom-radio-button"
    >
      {{ option.label }}
    </n-radio-button>
  </n-radio-group>
</template>

<script setup>
import { computed, watch, nextTick } from "vue";
import { useDictOptions } from "@/utils/dictUtils";

// 定义组件属性
const props = defineProps({
  // 当前选中的值
  modelValue: {
    type: [String, Number],
    default: null,
  },
  // 是否包含"不限"选项
  includeAll: {
    type: Boolean,
    default: true,
  },
  // 是否自动选中第一个选项
  autoSelectFirst: {
    type: Boolean,
    default: true,
  },
});

// 定义组件事件
const emit = defineEmits(["update:modelValue"]);

// 获取品牌选项（使用响应式字典数据）
const { options: allBrandOptions } = useDictOptions("vehicle_brand", false);

// 应用权限过滤和包含"不限"选项的逻辑
const brandOptions = computed(() => {
  const baseOptions = props.includeAll ? [{ label: "不限", value: null }] : [];
  return [...baseOptions, ...allBrandOptions.value];
});

// 处理值更新
const handleUpdate = (value) => {
  emit("update:modelValue", value);
};

// 监听品牌选项变化，自动设置默认选中第一个选项
watch(
  brandOptions,
  (newOptions) => {
    if (
      props.autoSelectFirst &&
      newOptions.length > 0 &&
      props.modelValue === null
    ) {
      // 如果当前没有选中值，自动选中第一个选项
      nextTick(() => {
        emit("update:modelValue", newOptions[0].value);
      });
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
/* 自定义单选按钮组样式 */
/* 自定义单选按钮组样式已移至全局样式文件 src/assets/styles/global.css */
</style>
