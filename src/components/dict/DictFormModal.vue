<template>
  <n-modal
    v-model:show="modalVisible"
    :title="isEdit ? '编辑字典' : '新增字典'"
    preset="card"
    :style="{ width: '600px' }"
    :mask-closable="false"
  >
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <!-- 网格布局：3行x2列 -->
      <n-grid :cols="2" :x-gap="16" :y-gap="16">
        <!-- 第一行 -->
        <n-grid-item>
          <n-form-item label="字典编码" path="dict_code">
            <n-input
              v-model:value="form.dict_code"
              placeholder="请输入字典编码"
              :disabled="isEdit"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="字典名称" path="dict_name">
            <n-input
              v-model:value="form.dict_name"
              placeholder="请输入字典名称"
            />
          </n-form-item>
        </n-grid-item>

        <!-- 第二行 -->
        <n-grid-item>
          <n-form-item label="可用范围" path="scope">
            <n-select
              v-model:value="form.scope"
              :options="scopeOptions"
              placeholder="请选择可用范围"
              @update:value="handleScopeChange"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item 
            v-if="form.scope === 'specific_org'" 
            label="业务机构" 
            path="org_ids"
          >
            <div class="org-selector-container">
              <n-button
                @click="showOrgSelector = true"
                :disabled="form.scope !== 'specific_org'"
                style="width: 100%"
              >
                {{ selectedOrgsText || '请选择业务机构' }}
              </n-button>
            </div>
          </n-form-item>
          <div v-else style="height: 34px;"></div>
        </n-grid-item>

        <!-- 第三行 -->
        <n-grid-item :span="2">
          <n-form-item label="备注" path="remark">
            <n-input
              v-model:value="form.remark"
              type="textarea"
              placeholder="请输入备注"
              :rows="3"
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>
    </n-form>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSave" :loading="loading">
          保存
        </n-button>
      </n-space>
    </template>

    <!-- 业务机构选择器 -->
    <BizOrgSelector
      v-model:visible="showOrgSelector"
      title="选择业务机构"
      :single="false"
      :selected-orgs="selectedOrgs"
      @select="handleOrgSelect"
      @cancel="showOrgSelector = false"
    />
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import {
  NModal,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NButton,
  NSpace,
  NGrid,
  NGridItem,
} from "naive-ui";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

// 定义组件事件
const emit = defineEmits(["update:visible", "save", "cancel"]);

// 组件状态
const formRef = ref(null);
const showOrgSelector = ref(false);
const selectedOrgs = ref([]);

// 表单数据
const form = reactive({
  dict_code: "",
  dict_name: "",
  scope: "group_available", // 默认集团可用
  org_ids: "",
  remark: "",
});

// 可用范围选项
const scopeOptions = [
  { label: "集团通用", value: "group_available" },
  { label: "机构专用", value: "specific_org" },
];

// 表单验证规则
const rules = {
  dict_code: [
    { required: true, message: "请输入字典编码", trigger: "blur" },
    {
      pattern: /^[a-zA-Z0-9_]+$/,
      message: "字典编码只能包含字母、数字和下划线",
      trigger: "blur",
    },
  ],
  dict_name: [{ required: true, message: "请输入字典名称", trigger: "blur" }],
  scope: [{ required: true, message: "请选择可用范围", trigger: "change" }],
  org_ids: [
    {
      validator: (rule, value) => {
        if (form.scope === "specific_org" && (!value || value.trim() === "")) {
          return new Error("请选择业务机构");
        }
        return true;
      },
      trigger: "change",
    },
  ],
};

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

// 选中机构的显示文本
const selectedOrgsText = computed(() => {
  if (selectedOrgs.value.length === 0) return "";
  if (selectedOrgs.value.length === 1) {
    return selectedOrgs.value[0].orgName || selectedOrgs.value[0].name;
  }
  return `已选择 ${selectedOrgs.value.length} 个机构`;
});

// 监听表单数据变化
watch(
  () => props.formData,
  (newData) => {
    if (newData) {
      Object.assign(form, {
        dict_code: newData.dict_code || "",
        dict_name: newData.dict_name || "",
        scope: newData.scope || "group_available",
        org_ids: newData.org_ids || "",
        remark: newData.remark || "",
      });

      // 如果有机构ID，需要解析并设置选中的机构
      if (newData.org_ids && newData.org_names) {
        const orgIds = newData.org_ids.split(",");
        const orgNames = newData.org_names.split(",");
        selectedOrgs.value = orgIds.map((id, index) => ({
          id: parseInt(id),
          orgName: orgNames[index] || `机构${id}`,
        }));
      } else {
        selectedOrgs.value = [];
      }
    }
  },
  { immediate: true, deep: true }
);

// 处理可用范围变化
const handleScopeChange = (value) => {
  if (value !== "specific_org") {
    form.org_ids = "";
    selectedOrgs.value = [];
  }
};

// 处理机构选择
const handleOrgSelect = (orgs) => {
  selectedOrgs.value = orgs;
  form.org_ids = orgs.map((org) => org.id).join(",");
  showOrgSelector.value = false;
};

// 处理保存
const handleSave = () => {
  formRef.value?.validate((errors) => {
    if (!errors) {
      const formDataToSave = {
        ...form,
        org_names: selectedOrgs.value.map((org) => org.orgName || org.name).join(","),
      };
      emit("save", formDataToSave);
    }
  });
};

// 处理取消
const handleCancel = () => {
  emit("cancel");
};
</script>

<style scoped>
.org-selector-container {
  width: 100%;
}

:deep(.n-form-item-label) {
  font-weight: 500;
  margin-bottom: 8px;
}

:deep(.n-grid-item) {
  display: flex;
  flex-direction: column;
}

:deep(.n-form-item) {
  margin-bottom: 0;
  flex: 1;
}
</style>
