<template>
  <n-modal
    :show="show"
    @update:show="$emit('update:show', $event)"
    :title="isEditMode ? '编辑应付账款' : '新增应付账款'"
    preset="card"
    style="width: 600px"
    :mask-closable="false"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="应付机构" path="payableOrgId">
        <n-space align="center" style="width: 100%">
          <n-input
            :value="selectedOrgName"
            placeholder="请选择应付机构"
            readonly
            style="flex: 1"
          />
          <n-button @click="showOrgSelector">选择机构</n-button>
        </n-space>
      </n-form-item>

      <n-form-item label="应付科目" path="payableSubject">
        <n-select
          v-model:value="formData.payableSubject"
          :options="subjectOptions"
          placeholder="请选择应付科目"
          :loading="subjectLoading"
          @update:value="handleSubjectChange"
        />
      </n-form-item>

      <n-form-item label="应付对象" path="payableTarget">
        <n-select
          v-model:value="formData.payableTarget"
          :options="targetOptions"
          placeholder="请选择应付对象"
          @update:value="handleTargetChange"
        />
      </n-form-item>

      <n-form-item label="应付金额（元）" path="payableAmount">
        <n-input-number
          v-model:value="formData.payableAmount"
          :min="0.01"
          :precision="2"
          button-placement="both"
          :step="1000"
          @update:value="handleAmountChange"
          style="width: 100%"
          :default-value="0.01"
          clearable
        >
          <template #prefix> ¥ </template>
        </n-input-number>
      </n-form-item>

      <n-form-item label="付款摘要" path="payableSummary">
        <n-input
          v-model:value="formData.payableSummary"
          type="textarea"
          placeholder="请输入付款摘要"
          :rows="3"
          :maxlength="200"
          show-count
        />
      </n-form-item>

      <n-form-item label="应付金额（大写）">
        <n-input :value="amountInChinese" disabled />
      </n-form-item>
    </n-form>

    <!-- 应付机构选择器 -->
    <biz-org-selector
      :visible="orgSelectorVisible"
      @update:visible="orgSelectorVisible = $event"
      title="选择应付机构"
      :single="true"
      @select="handleOrgSelect"
      @cancel="orgSelectorVisible = false"
    />

    <template #footer>
      <n-space justify="end">
        <n-button @click="$emit('update:show', false)">取消</n-button>
        <n-button type="primary" @click="handleSave" :loading="saving">
          确定
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import { getDictOptions } from "@/api/dict";
import { feeSubjectApi } from "@/api/feeSubject";

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    required: true,
  },
  amountInChinese: {
    type: String,
    default: "",
  },
  saving: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: "add", // 'add' 或 'edit'
    validator: (value) => ["add", "edit"].includes(value),
  },
});

// Emits
const emit = defineEmits([
  "update:show",
  "save",
  "org-change",
  "subject-change",
  "target-change",
  "amount-change",
]);

// Refs
const formRef = ref(null);
const orgSelectorVisible = ref(false);
const selectedOrgName = ref("");
const subjectOptions = ref([]);
const targetOptions = ref([]);
const subjectLoading = ref(false);

// Computed
const isEditMode = computed(() => props.mode === "edit");

// 表单验证规则
const rules = {
  payableOrgId: {
    required: true,
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择应付机构");
      }
      return true;
    },
    trigger: ["blur", "change"],
  },
  payableSubject: {
    required: true,
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择应付科目");
      }
      return true;
    },
    trigger: ["blur", "change"],
  },
  payableTarget: {
    required: true,
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择应付对象");
      }
      return true;
    },
    trigger: ["blur", "change"],
  },
  payableAmount: {
    required: true,
    validator: (_, value) => {
      if (value === null || value === undefined || value === "") {
        return new Error("请输入应付金额");
      }
      if (typeof value === "number" && value < 0.01) {
        return new Error("应付金额必须大于0");
      }
      return true;
    },
    trigger: ["blur", "change", "input"],
  },
  payableSummary: {
    required: true,
    validator: (_, value) => {
      if (!value || value.trim() === "") {
        return new Error("请输入付款摘要");
      }
      return true;
    },
    trigger: ["blur", "input"],
  },
};

// 初始化数据
onMounted(async () => {
  await loadTargetOptions();
  await loadSubjectOptions();
});

// 监听对话框显示状态和模式，新增时清除表单数据
watch(
  () => [props.show, props.mode],
  ([newShow, newMode]) => {
    if (newShow && newMode === "add") {
      // 新增模式下清除表单数据
      clearFormData();
    }
  }
);

// Methods
function clearFormData() {
  // 清除表单数据
  props.formData.payableOrgId = null;
  props.formData.payableSubject = null;
  props.formData.payableTarget = null;
  props.formData.payableAmount = null;
  props.formData.payableSummary = "";

  // 清除接口提交字段
  props.formData.feeId = null;
  props.formData.payableOrgName = "";
  props.formData.feeTarget = "";
  props.formData.feeAmount = null;

  // 清除相关显示数据
  selectedOrgName.value = "";
  // 注意：不清除 subjectOptions，因为科目列表是全局的，不依赖机构
}

function showOrgSelector() {
  orgSelectorVisible.value = true;
}

function handleOrgSelect(selectedOrgs) {
  if (selectedOrgs && selectedOrgs.length > 0) {
    const org = selectedOrgs[0];
    props.formData.payableOrgId = org.id;
    selectedOrgName.value = org.orgName;
    emit("org-change", org);
  }
  orgSelectorVisible.value = false;
}

function handleSubjectChange(value) {
  emit("subject-change", value);
}

function handleTargetChange(value) {
  emit("target-change", value);
}

function handleAmountChange(value) {
  emit("amount-change", value);
}

function handleSave() {
  console.log("=== PayableDialog handleSave 开始 ===");
  console.log("提交前的表单数据:", JSON.stringify(props.formData, null, 2));

  // 在提交前，填充接口所需的字段
  if (props.formData.payableSubject) {
    props.formData.feeId = props.formData.payableSubject;
    console.log("设置 feeId:", props.formData.feeId);
  }

  // 填充应付机构ID和名称
  if (props.formData.payableOrgId) {
    props.formData.payableOrgName = selectedOrgName.value;
    console.log("设置 payableOrgName:", props.formData.payableOrgName);
  }

  // 填充应付对象名称（从选项中获取对应的label）
  if (props.formData.payableTarget) {
    const targetOption = targetOptions.value.find(
      (option) => option.value === props.formData.payableTarget
    );
    props.formData.feeTarget = targetOption
      ? targetOption.label
      : props.formData.payableTarget;
    console.log("设置 feeTarget:", props.formData.feeTarget);
  }

  // 将金额从元转换为分
  if (props.formData.payableAmount && props.formData.payableAmount > 0) {
    const amountInYuan = Number(props.formData.payableAmount);
    const amountInFen = Math.round(amountInYuan * 100);

    props.formData.feeAmount = amountInFen;
    console.log(`金额转换: ${amountInYuan}元 → ${amountInFen}分`);
  }

  console.log("=== 最终表单数据 ===");
  console.log(JSON.stringify(props.formData, null, 2));

  // 创建一个专门用于接口提交的数据对象
  const submitData = {
    feeId: props.formData.feeId,
    payableOrgId: props.formData.payableOrgId,
    payableOrgName: props.formData.payableOrgName,
    feeTarget: props.formData.feeTarget,
    feeAmount: props.formData.feeAmount,
    payableSummary: props.formData.payableSummary,
  };

  console.log("=== 接口提交数据 ===");
  console.log(JSON.stringify(submitData, null, 2));

  // 将提交数据也放到 formData 中，确保父组件能获取到
  Object.assign(props.formData, submitData);

  console.log("=== PayableDialog handleSave 结束 ===");
  emit("save");
}

// 加载应付对象选项
async function loadTargetOptions() {
  try {
    const response = await getDictOptions("payable_target");
    if (response.code === 200) {
      targetOptions.value = response.data.map((item) => ({
        label: item.optionLabel,
        value: item.optionValue,
      }));
    }
  } catch (error) {
    console.error("加载应付对象选项失败:", error);
  }
}

// 加载应付科目选项
async function loadSubjectOptions() {
  subjectLoading.value = true;
  try {
    const response = await feeSubjectApi.getFeeSubjectList({
      page: 1,
      size: 500, // 获取所有科目
    });
    if (response.code === 200) {
      subjectOptions.value = response.data.list
        .filter((item) => item.payable) // 只获取可付款的科目
        .map((item) => ({
          label: item.subjectName,
          value: item.id,
        }));
    }
  } catch (error) {
    console.error("加载应付科目选项失败:", error);
    subjectOptions.value = [];
  } finally {
    subjectLoading.value = false;
  }
}

// 暴露表单引用给父组件
defineExpose({
  formRef,
});
</script>

<style lang="scss" scoped>
// 组件样式可以根据需要添加
</style>
