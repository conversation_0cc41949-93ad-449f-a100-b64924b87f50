<template>
  <n-modal
    :show="visible"
    @update:show="$emit('update:visible', $event)"
    :title="title"
    preset="card"
    style="width: 60%"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <!-- 车辆信息区域 - 参考ProductInfoSection样式 -->
      <div class="section-container">
        <div class="section-title">
          <span class="title-text">车辆信息</span>
          <n-button
            v-if="!isEdit && !props.readonly"
            type="primary"
            size="small"
            @click="showSkuSelector = true"
            class="title-button"
          >
            <template #icon>
              <n-icon>
                <component :is="CarOutlineIcon" />
              </n-icon>
            </template>
            选择车型
          </n-button>
          <div style="flex: 1"></div>
        </div>
        <n-divider
          class="section-divider"
          style="
            height: 2px;
            background-image: linear-gradient(
              to right,
              var(--primary-color, #18a058) 0%,
              rgba(24, 160, 88, 0.1) 100%
            );
            border: none;
          "
        ></n-divider>

        <!-- 车辆信息表单 -->
        <n-grid :cols="3" :x-gap="16" :y-gap="16">
          <!-- 第一行：品牌、车系、配置 -->
          <n-grid-item>
            <n-form-item label="品牌" path="vehicleBrand">
              <n-input
                v-model:value="form.vehicleBrand"
                placeholder="车辆品牌"
                :readonly="props.readonly || isEdit"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="车系" path="vehicleSeries">
              <n-input
                v-model:value="form.vehicleSeries"
                placeholder="车辆车系"
                :readonly="props.readonly || isEdit"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="配置" path="vehicleConfig">
              <n-input
                v-model:value="form.vehicleConfig"
                placeholder="车辆配置"
                :readonly="props.readonly || isEdit"
              />
            </n-form-item>
          </n-grid-item>

          <!-- 第二行：颜色、VIN、库存成本 -->
          <n-grid-item>
            <n-form-item label="颜色" path="vehicleColor">
              <n-input
                v-model:value="form.vehicleColor"
                placeholder="车辆颜色"
                :readonly="props.readonly || isEdit"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="VIN" path="vin">
              <n-input
                v-model:value="form.vin"
                placeholder="车架号"
                :readonly="props.readonly || isEdit"
                maxlength="17"
                show-count
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="库存成本(元)" path="startBillPrice">
              <n-input-number
                button-placement="both"
                v-model:value="form.startBillPrice"
                placeholder="库存成本"
                :readonly="props.readonly || isEdit"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>

          <!-- 第三行：入库日期、库存状态、空白占位 -->
          <n-grid-item>
            <n-form-item label="入库日期" path="stockInDate">
              <n-date-picker
                v-model:value="form.stockInDate"
                type="date"
                clearable
                style="width: 100%"
                value-format="timestamp"
                placeholder="入库日期"
                :disabled="props.readonly || isEdit"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="库存状态" path="stockStatus">
              <n-select
                v-model:value="form.stockStatus"
                :options="stockStatusOptions"
                placeholder="库存状态"
                :disabled="props.readonly || isEdit"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <!-- 空白占位 -->
          </n-grid-item>

          <!-- 第四行：现金车标记、试乘试驾车标记+标记日期、空白占位 -->
          <n-grid-item>
            <n-form-item label="现金车标记">
              <n-switch
                v-model:value="form.isCashVehicle"
                :disabled="props.readonly"
                size="medium"
              >
                <template #checked>是</template>
                <template #unchecked>否</template>
              </n-switch>
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <div class="trial-vehicle-container">
              <div class="trial-switch-wrapper">
                <n-form-item label="试乘试驾车">
                  <n-switch
                    v-model:value="form.isTrialVehicle"
                    :disabled="props.readonly"
                    size="medium"
                    @update:value="handleTrialVehicleChange"
                  >
                    <template #checked>是</template>
                    <template #unchecked>否</template>
                  </n-switch>
                </n-form-item>
              </div>
              <div class="trial-date-wrapper">
                <n-form-item label="标记日期">
                  <n-date-picker
                    v-model:value="form.trialBeginDate"
                    type="date"
                    clearable
                    style="width: 210px"
                    value-format="timestamp"
                    placeholder="请选择标记日期"
                    :disabled="props.readonly || !form.isTrialVehicle"
                    :class="{
                      'trial-date-field': true,
                      'trial-date-visible': form.isTrialVehicle,
                    }"
                  />
                </n-form-item>
              </div>
            </div>
          </n-grid-item>
          <n-grid-item>
            <!-- 空白占位 -->
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 调拨记录区域 - 只在查看模式下显示 -->
      <div
        v-if="props.readonly && transferRecords.length > 0"
        class="section-container"
      >
        <div class="section-title">
          <span class="title-text">调拨记录</span>
          <div style="flex: 1"></div>
        </div>
        <n-divider
          class="section-divider"
          style="
            height: 2px;
            background-image: linear-gradient(
              to right,
              var(--primary-color, #18a058) 0%,
              rgba(24, 160, 88, 0.1) 100%
            );
            border: none;
          "
        ></n-divider>

        <!-- 调拨记录表格 -->
        <n-data-table
          :columns="transferColumns"
          :data="transferRecords"
          :pagination="false"
          size="small"
          striped
          :max-height="200"
          virtual-scroll
        />
      </div>
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">{{
          props.readonly ? "关闭" : "取消"
        }}</n-button>
        <n-button v-if="!props.readonly" type="primary" @click="handleSave"
          >确定</n-button
        >
      </n-space>
    </template>

    <!-- 车型SKU选择器 -->
    <vehicle-s-k-u-selector
      v-model:visible="showSkuSelector"
      @select="handleVehicleSkuSelect"
    />
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, markRaw, h } from "vue";
import { useMessage, NTag } from "naive-ui";
import { CarOutline } from "@vicons/ionicons5";
import stocksApi from "@/api/stocks";
import VehicleSKUSelector from "@/components/inventory/VehicleSKUSelector.vue";
import { stockStatusUtils } from "@/utils/dictUtils";
import { formatDate } from "@/utils/dateUtils";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const CarOutlineIcon = markRaw(CarOutline);

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  stockId: {
    type: [String, Number],
    default: null,
  },
  stockData: {
    type: Object,
    default: null,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(["update:visible", "success"]);

// 响应式数据
const message = useMessage();
const formRef = ref(null);
const showSkuSelector = ref(false);
const loading = ref(false);

// 调拨记录数据
const transferRecords = ref([]);

// 库存状态选项
const stockStatusOptions = computed(() => stockStatusUtils.getOptions(false));

// 调拨状态映射
const transferStatusMap = {
  pending: { text: "待确认", type: "warning" },
  received: { text: "已接收", type: "success" },
  rejected: { text: "已拒绝", type: "error" },
};

// 调拨类型映射
const transferTypeMap = {
  INTERNAL: "内部调拨",
  EXTERNAL: "外部调拨",
};

// 调拨记录表格列配置
const transferColumns = [
  {
    title: "发起时间",
    key: "createdTime",
    width: 150,
    render(row) {
      return formatDate(row.createdTime, "YYYY-MM-DD HH:mm:ss");
    },
  },
  {
    title: "发起方",
    key: "sourceOrgName",
    width: 120,
    render(row) {
      return row.sourceOrgName || "-";
    },
  },
  {
    title: "发起人",
    key: "creatorAgentName",
    width: 100,
    render(row) {
      return row.creatorAgentName || "-";
    },
  },
  {
    title: "接收时间",
    key: "updatedTime",
    width: 150,
    render(row) {
      return row.updatedTime
        ? formatDate(row.updatedTime, "YYYY-MM-DD HH:mm:ss")
        : "-";
    },
  },
  {
    title: "接收方",
    key: "targetOrgName",
    width: 120,
    render(row) {
      return row.targetOrgName || "-";
    },
  },
  {
    title: "接收人",
    key: "receptAgentName",
    width: 100,
    render(row) {
      return row.receptAgentName || "-";
    },
  },
  {
    title: "调拨状态",
    key: "transferStatus",
    width: 100,
    render(row) {
      const status = transferStatusMap[row.transferStatus] || {
        text: row.transferStatus || "未知",
        type: "default",
      };
      return h(
        NTag,
        {
          type: status.type,
          size: "small",
          round: true,
        },
        { default: () => status.text }
      );
    },
  },
  {
    title: "调拨类型",
    key: "transferType",
    width: 100,
    render(row) {
      return transferTypeMap[row.transferType] || row.transferType || "-";
    },
  },
  {
    title: "调拨备注",
    key: "remark",
    width: 150,
    render(row) {
      return row.remark || "-";
    },
  },
];

// 计算属性
const isEdit = computed(() => !!props.stockId);
const title = computed(() => {
  if (props.readonly) {
    return "查看库存详情";
  }
  return isEdit.value ? "编辑库存" : "新增库存";
});

// 表单数据
const form = reactive({
  id: null,
  vehicleSku: null,
  vehicleInfo: null,
  vehicleBrand: "",
  vehicleSeries: "",
  vehicleConfig: "",
  vehicleColor: "",
  vin: "",
  startBillPrice: null,
  stockInDate: null,
  stockStatus: null,
  isCashVehicle: false, // 现金车标记
  isTrialVehicle: false, // 试乘试驾车标记
  trialBeginDate: null, // 试乘试驾标记日期
});

// 表单验证规则 - 只有新增模式且非只读时才需要验证
const rules = computed(() => {
  if (isEdit.value || props.readonly) {
    // 编辑模式和只读模式下不需要验证
    return {};
  }

  // 新增模式下的验证规则
  return {
    vehicleSku: {
      required: true,
      message: "请选择车型",
      trigger: ["blur", "change"],
      validator: (_, value) => {
        if (!value) {
          return new Error("请选择车型");
        }
        return true;
      },
    },
    vehicleBrand: {
      required: true,
      message: "请选择车型或输入品牌",
      trigger: ["blur", "input"],
      validator: (_, value) => {
        if (!value || value.trim() === "") {
          return new Error("请选择车型或输入品牌");
        }
        return true;
      },
    },
    vehicleSeries: {
      required: true,
      message: "请选择车型或输入车系",
      trigger: ["blur", "input"],
      validator: (_, value) => {
        if (!value || value.trim() === "") {
          return new Error("请选择车型或输入车系");
        }
        return true;
      },
    },
    vehicleConfig: {
      required: true,
      message: "请选择车型或输入配置",
      trigger: ["blur", "input"],
      validator: (_, value) => {
        if (!value || value.trim() === "") {
          return new Error("请选择车型或输入配置");
        }
        return true;
      },
    },
    vehicleColor: {
      required: true,
      message: "请输入车辆颜色",
      trigger: ["blur", "input"],
      validator: (_, value) => {
        if (!value || value.trim() === "") {
          return new Error("请输入车辆颜色");
        }
        return true;
      },
    },
    vin: {
      required: true,
      message: "请输入VIN",
      trigger: ["blur", "input"],
      validator: (_, value) => {
        if (!value || value.trim() === "") {
          return new Error("请输入VIN");
        }
        if (value.length !== 17) {
          return new Error("VIN必须是17位字符");
        }
        return true;
      },
    },
    startBillPrice: {
      required: true,
      message: "请输入库存成本",
      trigger: ["blur", "change"],
      validator: (_, value) => {
        if (value === null || value === undefined || value === "") {
          return new Error("请输入库存成本");
        }
        if (value <= 0) {
          return new Error("库存成本必须大于0");
        }
        return true;
      },
    },
    stockInDate: {
      required: true,
      message: "请选择入库日期",
      trigger: ["blur", "change"],
      validator: (_, value) => {
        if (!value) {
          return new Error("请选择入库日期");
        }
        return true;
      },
    },
    stockStatus: {
      required: true,
      message: "请选择库存状态",
      trigger: ["blur", "change"],
      validator: (_, value) => {
        if (!value) {
          return new Error("请选择库存状态");
        }
        return true;
      },
    },
  };
});

// 重置表单
const resetForm = () => {
  form.id = null;
  form.vehicleSku = null;
  form.vehicleInfo = null;
  form.vehicleBrand = "";
  form.vehicleSeries = "";
  form.vehicleConfig = "";
  form.vehicleColor = "";
  form.vin = "";
  form.startBillPrice = null;
  form.stockInDate = null;
  form.stockStatus = null;
  form.isCashVehicle = false;
  form.isTrialVehicle = false;
  form.trialBeginDate = null;

  // 清空调拨记录
  transferRecords.value = [];

  // 重置表单验证状态
  nextTick(() => {
    formRef.value?.restoreValidation();
  });
};

// 处理车型SKU选择
const handleVehicleSkuSelect = (skuData) => {
  if (skuData) {
    form.vehicleSku = skuData.id;
    form.vehicleInfo = {
      brandName: skuData.brand || skuData.brandName,
      seriesName: skuData.series || skuData.seriesName,
      modelName: skuData.configName || skuData.modelName,
      configCode: skuData.configCode,
      colorCode: skuData.colorCode,
    };

    // 填充车辆信息字段
    form.vehicleBrand = skuData.brand || skuData.brandName || "";
    form.vehicleSeries = skuData.series || skuData.seriesName || "";
    form.vehicleConfig = skuData.configName || skuData.modelName || "";
    form.vehicleColor = skuData.colorCode || "";

    // 启票价格默认填充到库存成本字段
    if (skuData.sbPrice && !form.startBillPrice) {
      form.startBillPrice = skuData.sbPrice;
    }
  }
  showSkuSelector.value = false;
};

// 处理试乘试驾车开关变化
const handleTrialVehicleChange = (value) => {
  console.log("试乘试驾车开关变化:", value);
  if (value) {
    // 开启试乘试驾车时，设置当前日期为默认标记日期（时间戳格式）
    const today = new Date();
    form.trialBeginDate = today.getTime();
    console.log("设置标记日期:", form.trialBeginDate);
  } else {
    // 关闭试乘试驾车时，清空标记日期
    form.trialBeginDate = null;
    console.log("清空标记日期");
  }
};

// 日期字符串转换为时间戳
const convertDateStringToTimestamp = (dateString) => {
  if (!dateString) return null;
  try {
    // 处理多种日期格式：
    // "2025-06-27 00:00:00" 或 "2025-06-27"
    const date = new Date(dateString);
    return date.getTime();
  } catch (error) {
    console.error("日期转换失败:", error);
    return null;
  }
};

// 时间戳转换为long类型（接口期望的格式）
const convertTimestampToLong = (timestamp) => {
  if (!timestamp) return null;
  try {
    // 如果已经是数字类型的时间戳，直接返回
    if (typeof timestamp === "number") {
      return timestamp;
    }
    // 如果是Date对象或字符串，转换为时间戳
    const date = new Date(timestamp);
    return date.getTime();
  } catch (error) {
    console.error("时间戳转换失败:", error);
    return null;
  }
};

// 加载库存详情（编辑时）
const loadStockDetail = async (id) => {
  if (!id) return;

  try {
    loading.value = true;
    const response = await stocksApi.getStockDetail(id);

    if (response.code === 200 && response.data) {
      const data = response.data;
      form.id = data.id;
      form.vin = data.vin || "";
      form.startBillPrice = data.stockAmount ? data.stockAmount / 100 : 0; // 转换为元

      // 修复日期格式问题 - 转换为时间戳
      form.stockInDate = convertDateStringToTimestamp(data.createTime);
      form.stockStatus = data.stockStatus || null;

      // 设置现金车标记
      form.isCashVehicle = data.stockType === "CASH";

      // 设置试乘试驾车标记
      form.isTrialVehicle = data.trialStatus === "trialing";
      // 试乘试驾开始日期 - 接口返回的是long类型时间戳，直接使用
      form.trialBeginDate = data.trialingBeginDate || null;

      // 从列表数据中获取车辆信息
      loadVehicleInfoFromListData();

      // 加载调拨记录（只在查看模式下加载）
      if (props.readonly && data.transfers && Array.isArray(data.transfers)) {
        // 只取最近5条记录，按创建时间倒序排列
        transferRecords.value = data.transfers
          .sort((a, b) => new Date(b.createdTime) - new Date(a.createdTime))
          .slice(0, 5);
      }
    } else {
      message.error(response.message || "加载库存详情失败");
    }
  } catch (error) {
    console.error("加载库存详情失败:", error);
    message.error("加载库存详情失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 从列表数据中加载车辆信息
const loadVehicleInfoFromListData = () => {
  if (props.stockData) {
    const data = props.stockData;
    // 从列表数据中获取车辆信息
    form.vehicleBrand = data.brand || "";
    form.vehicleSeries = data.series || "";
    form.vehicleConfig = data.configName || "";
    // 优先使用colorCode，如果没有则使用color字段
    form.vehicleColor = data.colorCode || data.color || "";

    // 如果有详细的车辆信息对象
    if (data.vehicleInfo) {
      form.vehicleInfo = {
        brandName: data.vehicleInfo.brandName,
        seriesName: data.vehicleInfo.seriesName,
        modelName: data.vehicleInfo.modelName,
        configCode: data.vehicleInfo.configCode,
        colorCode: data.vehicleInfo.colorCode,
      };

      // 优先使用详细信息
      form.vehicleBrand = data.vehicleInfo.brandName || form.vehicleBrand;
      form.vehicleSeries = data.vehicleInfo.seriesName || form.vehicleSeries;
      form.vehicleConfig = data.vehicleInfo.modelName || form.vehicleConfig;
      form.vehicleColor = data.vehicleInfo.colorCode || form.vehicleColor;
    }
  }
};

// 保存数据
const handleSave = () => {
  // 编辑模式下不需要表单验证，直接提交
  if (isEdit.value) {
    submitEditData();
  } else {
    // 新增模式下需要表单验证
    formRef.value?.validate(async (errors) => {
      if (errors) {
        return;
      }
      submitNewData();
    });
  }
};

// 提交编辑数据
const submitEditData = async () => {
  try {
    loading.value = true;

    // 编辑模式提交现金车标记和试乘试驾车标记
    const submitData = {
      stockType: form.isCashVehicle ? "CASH" : "none",
      trialStatus: form.isTrialVehicle ? "trialing" : "none",
      trialingBeginDate: form.isTrialVehicle
        ? convertTimestampToLong(form.trialBeginDate)
        : null,
    };

    // 调试信息：打印提交的数据
    console.log("提交的库存编辑数据:", {
      id: form.id,
      ...submitData,
    });

    const response = await stocksApi.updateStock({
      id: form.id,
      ...submitData,
    });

    if (response.code === 200) {
      message.success("库存更新成功");
      emit("success");
      handleCancel();
    } else {
      message.error(response.message || "更新失败");
    }
  } catch (error) {
    console.error("更新失败:", error);
    message.error("更新失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 提交新增数据
const submitNewData = async () => {
  try {
    loading.value = true;

    const submitData = {
      skuId: form.vehicleSku, // 字段名改为skuId
      vin: form.vin,
      stockAmount: Math.round(form.startBillPrice * 100), // 转换为分，字段名改为stockAmount
      stockInDate: form.stockInDate,
      stockStatus: form.stockStatus,
      stockType: form.isCashVehicle ? "CASH" : "none",
      trialStatus: form.isTrialVehicle ? "trialing" : "none",
      trialingBeginDate: form.isTrialVehicle
        ? convertTimestampToLong(form.trialBeginDate)
        : null,
    };

    // 调试信息：打印提交的数据
    console.log("提交的库存新增数据:", submitData);

    const response = await stocksApi.addStock(submitData);

    if (response.code === 200) {
      message.success("库存创建成功");
      emit("success");
      handleCancel();
    } else {
      message.error(response.message || "创建失败");
    }
  } catch (error) {
    console.error("创建失败:", error);
    message.error("创建失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit("update:visible", false);
  resetForm();
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      if (props.stockId) {
        // 编辑模式，加载数据
        loadStockDetail(props.stockId);
      } else {
        // 新增模式，重置表单
        resetForm();
      }
    }
  }
);

// 监听stockData变化，用于从列表传入车辆信息
watch(
  () => props.stockData,
  (newData) => {
    if (newData && props.visible && props.stockId) {
      // 当有新的列表数据传入且处于编辑模式时，更新车辆信息
      loadVehicleInfoFromListData();
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.section-container {
  margin: 16px 0;

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .title-text {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color-1);
      margin-right: 12px;
    }

    .title-button {
      margin-left: 8px;
    }
  }

  .section-divider {
    margin: 8px 0 16px 0;
  }
}

// 试乘试驾车容器样式
.trial-vehicle-container {
  display: flex;
  align-items: flex-start;
  gap: 20px;

  .trial-switch-wrapper {
    flex: 0 0 auto;
    min-width: 120px;
  }

  .trial-date-wrapper {
    flex: 1;
    max-width: 250px;
  }

  // 确保两个form-item的label和控件都对齐
  .trial-switch-wrapper .n-form-item,
  .trial-date-wrapper .n-form-item {
    margin-bottom: 0;
  }
}

// 试乘试驾车标记日期字段样式
.trial-date-field {
  transition: opacity 0.3s ease;

  &:not(.trial-date-visible) {
    opacity: 0.6;
  }

  &.trial-date-visible {
    opacity: 1;
  }
}
</style>
