<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">基本信息</span>
    </div>
    <n-divider class="section-divider"></n-divider>
    <n-grid :cols="4" :x-gap="16" :y-gap="12">
      <n-grid-item>
        <n-form-item label="订单编号">
          <div class="detail-item">
            <span class="order-sn">{{ orderDetail.orderSn }}</span>
            <n-button
              quaternary
              circle
              size="small"
              @click="copyToClipboard(orderDetail.orderSn)"
            >
              <template #icon>
                <n-icon color="#18a058">
                  <component :is="CopyOutlineIcon" />
                </n-icon>
              </template>
            </n-button>
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="订单类型">
          <div class="detail-item">
            {{ getOrderTypeText(orderDetail.orderType) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="订单日期">
          <div class="detail-item">
            {{ formatDate(orderDetail.orderDate) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="订单状态">
          <div class="detail-item">
            <n-tag
              :type="getOrderStatusType(orderDetail.orderStatus)"
              size="small"
            >
              {{ getOrderStatusText(orderDetail.orderStatus) }}
            </n-tag>
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="支付方式">
          <div class="detail-item">
            {{ orderDetail.paymentMethod === "FULL" ? "全款" : "分期" }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="交付日期">
          <div class="detail-item">
            {{ formatDate(orderDetail.deliveryDate) }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="创建人">
          <div class="detail-item">
            {{ orderDetail.creatorName || "未知" }}
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="最后编辑人">
          <div class="detail-item">
            {{ orderDetail.editorName || "未知" }}
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { markRaw } from "vue";
import {
  NGrid,
  NGridItem,
  NFormItem,
  NDivider,
  NTag,
  NButton,
  NIcon,
} from "naive-ui";
import { CopyOutline } from "@vicons/ionicons5";
import messages from "@/utils/messages";
import { getDictLabel } from "@/utils/dictUtils";

const CopyOutlineIcon = markRaw(CopyOutline);

const props = defineProps({
  orderDetail: {
    type: Object,
    required: true,
  },
});

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "未设置";
  if (typeof dateStr === "number") {
    return new Date(dateStr).toLocaleDateString("zh-CN");
  }
  return dateStr.split(" ")[0];
};

// 复制到剪贴板
const copyToClipboard = (text) => {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      messages.success("复制成功");
    })
    .catch(() => {
      messages.error("复制失败");
    });
};

// 获取订单状态文本
const getOrderStatusText = (status) => {
  if (!status) return "未知";
  const orderStatusOptions = dictOptions["order_status"] || [];
  const option = orderStatusOptions.find((item) => item.optionValue === status);
  return option ? option.optionLabel : status;
};

// 获取订单状态类型
const getOrderStatusType = (status) => {
  if (!status) return "default";
  const orderStatusOptions = dictOptions["order_status"] || [];
  const option = orderStatusOptions.find((item) => item.optionValue === status);
  return option ? option.type : "default";
};

// 获取订单类型文本
const getOrderTypeText = (type) => {
  const typeMap = {
    normal: "销售订单",
    deposit: "定金订单",
  };
  return typeMap[type] || "销售订单";
};
</script>

<style scoped>
.section-container {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-divider {
  margin: 8px 0 16px 0;
  height: 2px;
  background-image: linear-gradient(
    to right,
    var(--primary-color, #18a058) 0%,
    rgba(24, 160, 88, 0.1) 100%
  );
  border: none;
}

.detail-item {
  min-height: 28px;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #333;
  font-size: 16.8px;
}

.order-sn {
  font-family: monospace;
  letter-spacing: 1px;
  font-weight: 600;
  margin-right: 8px;
}

:deep(.n-form-item .n-form-item-label) {
  font-weight: 500;
  color: #909399;
}

:deep(.n-form-item) {
  margin-bottom: 0;
}

:deep(.n-tag) {
  padding: 2px 10px;
  font-weight: 600;
}
</style>
