<template>
  <div v-if="sectionConfig.visible" class="section-container">
    <div class="section-title">
      <span class="title-text">客户信息</span>
      <n-button
        v-if="sectionConfig['show-customer-selector']"
        type="primary"
        size="small"
        @click="$emit('show-customer-selector')"
        class="title-button"
      >
        <template #icon>
          <n-icon>
            <component :is="PersonOutlineIcon" />
          </n-icon>
        </template>
        选择客户
      </n-button>
      <div style="flex: 1"></div>
      <!-- 添加一个占位元素，将剩余空间推到右侧 -->
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <n-grid :cols="4" :x-gap="16" :y-gap="6">
      <!-- 第一行 -->
      <n-grid-item v-if="isFieldVisible('customerType')">
        <n-form-item label="客户类型" path="customerType" required>
          <n-select
            v-model:value="form.customerType"
            :options="customerTypeOptions"
            placeholder="请选择客户类型"
            :disabled="!isFieldEditable('customerType') || isCustomerSelected"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('customerName')">
        <n-form-item label="客户名称" path="customerName" required>
          <n-input
            v-model:value="form.customerName"
            placeholder="请输入客户名称"
            :readonly="!isFieldEditable('customerName') || isCustomerSelected"
            @blur="handleCustomerNameBlur"
            maxlength="20"
            show-count
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('customerPhone')">
        <n-form-item label="联系电话" path="customerPhone" required>
          <n-input
            v-model:value="form.customerPhone"
            placeholder="请输入11位手机号码"
            :readonly="!isFieldEditable('customerPhone') || isCustomerSelected"
            @blur="handleCustomerPhoneBlur"
            @input="handleCustomerPhoneInput"
            maxlength="11"
          >
            <template #suffix v-if="customerFound">
              <n-icon color="#18a058" size="16">
                <component :is="CheckmarkCircleIcon" />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('customerCode')">
        <n-form-item label="客户编码" path="customerCode">
          <n-input
            v-model:value="form.customerCode"
            placeholder="系统自动生成"
            readonly
          />
        </n-form-item>
      </n-grid-item>

      <!-- 第二行 -->
      <n-grid-item v-if="isFieldVisible('salesAgentName')">
        <n-form-item label="销售顾问" path="salesAgentName">
          <n-input
            v-model:value="form.salesAgentName"
            placeholder="请选择销售顾问"
            readonly
          >
            <template #suffix>
              <n-button
                v-if="isFieldEditable('salesAgentName')"
                type="primary"
                size="tiny"
                @click="showsalesAgentNameSelector"
                style="margin-right: 4px"
                :disabled="isCustomerSelected"
              >
                选择
              </n-button>
            </template>
          </n-input>
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('salesOrgName')">
        <n-form-item label="销售单位" path="salesOrgName" required>
          <n-input
            v-model:value="form.salesOrgName"
            placeholder="请选择销售顾问后自动填充"
            readonly
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('salesStoreType')">
        <n-form-item label="销售地类型" path="salesStoreType" required>
          <n-select
            v-model:value="form.salesStoreType"
            :options="salesStoreTypeOptions"
            placeholder="请选择销售地类型"
            :disabled="!isFieldEditable('salesStoreType')"
            @update:value="handleSalesStoreTypeChange"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('salesStoreDetail')">
        <n-form-item
          :label="getSalesStoreDetailLabel()"
          path="salesStoreDetail"
        >
          <!-- 当销售地类型为直营店时，显示直营店下拉框 -->
          <n-select
            v-if="form.salesStoreType === '3S'"
            v-model:value="form.salesStoreDetail"
            :options="directStoreOptions"
            placeholder="请选择直营店"
            filterable
            :loading="directStoreLoading"
            :disabled="!isFieldEditable('salesStoreDetail')"
            @update:value="handleDirectStoreChange"
          />
          <!-- 当销售地类型为二网时，显示输入框 -->
          <n-input
            v-else-if="form.salesStoreType === '2S'"
            v-model:value="form.salesStoreDetail"
            placeholder="请输入二网备注"
            :readonly="!isFieldEditable('salesStoreDetail')"
          />
          <!-- 当销售地类型为单店时，留空 -->
          <div v-else style="height: 34px"></div>
        </n-form-item>
      </n-grid-item>

      <!-- 第三行 -->
      <n-grid-item v-if="isFieldVisible('depositType')">
        <n-form-item path="depositType" required>
          <template #label>
            <div style="display: flex; align-items: center; gap: 4px">
              <span>定金类型</span>
              <n-tooltip trigger="hover" placement="top">
                <template #trigger>
                  <n-icon size="14" style="color: #999; cursor: help">
                    <component :is="InformationCircleOutlineIcon" />
                  </n-icon>
                </template>
                <div>
                  <div>线下付定：产生应收-客户-新车定金</div>
                  <div>线上付定：产生应收-厂家-新车定金</div>
                </div>
              </n-tooltip>
            </div>
          </template>
          <n-select
            v-model:value="form.depositType"
            :options="depositTypeOptions"
            placeholder="请选择定金类型"
            :disabled="!isFieldEditable('depositType')"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('depositAmount')">
        <n-form-item label="已付定金(元)" path="depositAmount">
          <div style="display: flex; align-items: center; width: 100%">
            <n-input-number
              v-model:value="form.depositAmount"
              placeholder="请输入已付定金"
              style="flex: 1"
              :precision="2"
              :min="0"
              button-placement="both"
              :disabled="!isFieldEditable('depositAmount') || hasDepositData"
              :readonly="hasDepositData"
            />
            <n-checkbox
              v-if="isFieldVisible('depositDeductible')"
              v-model:checked="form.depositDeductible"
              style="margin-left: 10px; white-space: nowrap; width: 80px"
              :disabled="!isFieldEditable('depositDeductible')"
              >转车款</n-checkbox
            >
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>

  <!-- 销售顾问选择器 -->
  <biz-org-member-selector
    v-model:visible="salesAgentNameSelectorVisible"
    mode="single"
    title="选择销售顾问"
    @select="handlesalesAgentNameSelected"
  />
</template>

<script setup>
import { ref, computed, markRaw, watch, onMounted } from "vue";
import {
  NButton,
  NIcon,
  NGrid,
  NGridItem,
  NFormItem,
  NInput,
  NInputNumber,
  NCheckbox,
  NSelect,
  NTooltip,
  NDivider,
} from "naive-ui";
import {
  PersonOutline,
  InformationCircleOutline,
  CheckmarkCircle,
} from "@vicons/ionicons5";
import { useAdvancedDictOptions } from "@/composables/useAdvancedDict";
import { getPageDictConfig } from "@/config/dictConfig";
import { DICT_CODES } from "@/constants/dictConstants";
import BizOrgMemberSelector from "@/components/bizOrg/BizOrgMemberSelector.vue";
import customerApi from "@/api/customer";
import vehicleOrderApi from "@/api/vehicleOrder";
import { getBizOrgList } from "@/api/bizOrg";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const PersonOutlineIcon = markRaw(PersonOutline);
const InformationCircleOutlineIcon = markRaw(InformationCircleOutline);
const CheckmarkCircleIcon = markRaw(CheckmarkCircle);

// 定金类型选项
const depositTypeOptions = [
  { label: "线下付定", value: "offline" },
  { label: "线上付定", value: "online" },
];

// 获取页面字典配置
const pageConfig = getPageDictConfig("ORDER_EDIT");

// 客户类型选项 - 使用新的字典抽象架构
const { options: customerTypeOptions } = useAdvancedDictOptions(
  DICT_CODES.CUSTOMER_TYPE,
  pageConfig.customerType?.config || {}
);

// 销售地类型选项 - 使用新的字典抽象架构
const { options: salesStoreTypeOptions } = useAdvancedDictOptions(
  DICT_CODES.SALES_STORE_TYPE,
  pageConfig.salesStoreType?.config || {}
);

// 销售顾问选择器状态
const salesAgentNameSelectorVisible = ref(false);

// 客户查询状态
const customerFound = ref(false);

// 客户定金数据
const customerDepositData = ref(null);

// 定金字段动态可见性控制
const depositFieldsVisible = ref(true);

// 直营店相关状态
const directStoreOptions = ref([]);
const directStoreLoading = ref(false);

// 初始化定金字段可见性
const initializeDepositFieldsVisibility = () => {
  // 如果启用了定金查询功能
  if (sectionConfig.value["search-deposits"]) {
    const depositFields = ["depositAmount", "depositType", "depositDeductible"];

    // 检查这些字段是否都在配置中设为可见
    const allDepositFieldsVisible = depositFields.every((fieldName) => {
      const fieldConfig = sectionConfig.value.fields?.[fieldName];
      return fieldConfig?.visible !== false;
    });

    // 如果定金字段都可见，则先隐藏它们，等查询到定金信息后再显示
    if (allDepositFieldsVisible) {
      depositFieldsVisible.value = false;
      console.log("定金查询功能已启用，定金字段已隐藏，等待查询结果");
    }
  }
};

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    default: () => ({
      visible: true,
      editable: true,
      "show-customer-selector": true,
      "search-deposits": false,
      fields: {},
    }),
  },
});

// 计算属性：获取section配置
const sectionConfig = computed(() => {
  return (
    props.config || {
      visible: true,
      editable: true,
      "show-customer-selector": true,
      "search-deposits": false,
      fields: {},
    }
  );
});

// 工具函数：检查字段是否可见
const isFieldVisible = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  const baseVisible = fieldConfig?.visible !== false; // 默认可见

  // 如果启用了定金查询功能，需要动态控制定金相关字段的可见性
  if (sectionConfig.value["search-deposits"]) {
    const depositFields = ["depositAmount", "depositType", "depositDeductible"];
    if (depositFields.includes(fieldName)) {
      return baseVisible && depositFieldsVisible.value;
    }
  }

  return baseVisible;
};

// 工具函数：检查字段是否可编辑
const isFieldEditable = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  const sectionEditable = sectionConfig.value.editable !== false;
  const fieldEditable = fieldConfig?.editable !== false;
  return sectionEditable && fieldEditable; // 区域和字段都必须可编辑
};

// 计算属性：判断是否已选择客户
const isCustomerSelected = computed(() => {
  return props.form.customerId !== null && props.form.customerId !== undefined;
});

// 计算属性：判断是否有定金数据且启用了定金查询功能
const hasDepositData = computed(() => {
  return (
    sectionConfig.value["search-deposits"] && customerDepositData.value !== null
  );
});

// 获取销售地详情标签
const getSalesStoreDetailLabel = () => {
  if (props.form.salesStoreType === "3S") {
    return "直营店";
  } else if (props.form.salesStoreType === "2S") {
    return "二网备注";
  }
  return "";
};

// 处理销售地类型变化
const handleSalesStoreTypeChange = (value) => {
  // 清空销售地详情
  props.form.salesStoreDetail = null;

  // 如果选择直营店，加载直营店列表
  if (value === "3S") {
    fetchDirectStores();
  }
};

// 获取直营店列表
const fetchDirectStores = async () => {
  directStoreLoading.value = true;
  try {
    // 调用业务机构API，筛选直营店类型
    const response = await getBizOrgList({
      orgType: "3S", // 假设直营店的机构类型为3S
      listAll: true,
    });

    if (response.code === 200) {
      const stores = response.data.list || response.data || [];
      directStoreOptions.value = stores.map((store) => ({
        label: store.orgName,
        value: store.id,
        orgData: store,
      }));
    }
  } catch (error) {
    console.error("获取直营店列表失败:", error);
  } finally {
    directStoreLoading.value = false;
  }
};

// 处理直营店选择
const handleDirectStoreChange = (value) => {
  const selectedStore = directStoreOptions.value.find(
    (option) => option.value === value
  );
  if (selectedStore) {
    // 可以在这里处理选中直营店后的逻辑
    console.log("选中直营店:", selectedStore.orgData);
  }
};

// 监听联系电话变化，重置客户找到状态
watch(
  () => props.form.customerPhone,
  (newPhone, oldPhone) => {
    // 如果联系电话发生变化且不是通过接口绑定的变化，重置客户找到状态
    if (newPhone !== oldPhone && customerFound.value) {
      customerFound.value = false;
      // 清空customerId，确保表单校验能够正确触发
      props.form.customerId = null;
    }
  }
);

// 监听客户名称变化，重置客户状态
watch(
  () => props.form.customerName,
  (newName, oldName) => {
    // 如果客户名称发生变化且不是通过接口绑定的变化，重置客户状态
    if (newName !== oldName && customerFound.value) {
      customerFound.value = false;
      // 清空customerId，确保表单校验能够正确触发
      props.form.customerId = null;
    }
  }
);

// 监听customerId变化，更新客户编码并获取定金数据
watch(
  () => props.form.customerId,
  async (newCustomerId, oldCustomerId) => {
    // 更新客户编码
    if (newCustomerId) {
      // 如果有客户ID，显示客户ID作为客户编码
      props.form.customerCode = newCustomerId.toString();
    } else {
      // 如果没有客户ID，显示默认文本
      props.form.customerCode = "";
    }

    // 只有在启用定金查询功能且customerId存在且发生变化时，才调用接口获取定金订单数据
    if (
      sectionConfig.value["search-deposits"] &&
      newCustomerId &&
      newCustomerId !== oldCustomerId
    ) {
      await fetchCustomerDepositOrders(newCustomerId);
    }
  },
  { immediate: true }
);

// 监听定金类型变化，联动更新已付定金金额
watch(
  () => props.form.depositType,
  (newDepositType) => {
    // 只有在启用定金查询功能且有定金数据时才进行联动
    if (
      sectionConfig.value["search-deposits"] &&
      customerDepositData.value &&
      newDepositType
    ) {
      updateDepositAmount(newDepositType);
    }
  }
);

// 定义组件事件
defineEmits(["show-customer-selector"]);

// 组件挂载时初始化定金字段可见性
onMounted(() => {
  initializeDepositFieldsVisibility();
});

// 显示销售顾问选择器
const showsalesAgentNameSelector = () => {
  // 如果已选择客户，不允许修改销售顾问
  if (isCustomerSelected.value) {
    return;
  }
  salesAgentNameSelectorVisible.value = true;
};

// 处理销售顾问选择
const handlesalesAgentNameSelected = (member) => {
  if (member) {
    // 填充销售顾问信息
    props.form.salesAgentName = member.agentName;
    props.form.salesAgentId = member.agentId;

    // 填充销售单位信息（由机构成员选择器返回的数据自动填充）
    props.form.salesOrgId = member.biz_org_id;
    props.form.salesOrgName = member.orgName || "";
  }
  salesAgentNameSelectorVisible.value = false;
};

// 根据定金类型更新已付定金金额
const updateDepositAmount = (depositType) => {
  if (!customerDepositData.value) return;

  const depositData = customerDepositData.value;

  if (depositType === "offline" && depositData.offline) {
    props.form.depositAmount = depositData.offline.depositTotalAmount / 100; // 转换为元
    console.log("联动更新为线下定金金额:", props.form.depositAmount);
  } else if (depositType === "online" && depositData.online) {
    props.form.depositAmount = depositData.online.depositTotalAmount / 100; // 转换为元
    console.log("联动更新为线上定金金额:", props.form.depositAmount);
  }
};

// 处理客户名称失焦事件
const handleCustomerNameBlur = () => {
  // 如果客户名称被手动修改且之前有选择客户，清空客户ID
  if (customerFound.value && props.form.customerId) {
    customerFound.value = false;
    props.form.customerId = null;
    console.log("客户名称已修改，已清空customerId");
  }
};

// 处理手机号码输入事件
const handleCustomerPhoneInput = (value) => {
  // 只允许输入数字
  const numericValue = value.replace(/\D/g, "");
  if (numericValue !== value) {
    props.form.customerPhone = numericValue;
  }

  // 如果手机号码被手动修改且之前有选择客户，清空客户ID
  if (customerFound.value && props.form.customerId) {
    customerFound.value = false;
    props.form.customerId = null;
    console.log("手机号码已修改，已清空customerId");
  }
};

// 处理联系电话失焦事件
const handleCustomerPhoneBlur = async () => {
  // 检查必要字段是否已填写
  if (
    !props.form.customerName ||
    !props.form.customerPhone ||
    !props.form.customerType
  ) {
    return;
  }

  // 先验证手机号格式是否正确
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(props.form.customerPhone)) {
    // 手机号格式不正确，不执行客户查询
    console.log("手机号格式不正确，跳过客户查询");
    return;
  }

  // 如果已经选择了客户，不需要重复查询
  if (isCustomerSelected.value) {
    return;
  }

  try {
    // 调用接口查询客户ID
    const params = {
      customerName: props.form.customerName,
      customerPhone: props.form.customerPhone,
      customerType: props.form.customerType,
    };

    const response = await customerApi.getCustomerId(params);

    // 处理返回结果
    if (response && response.code === 200 && response.data) {
      // 客户存在，绑定客户信息到表单
      const customerData = response.data;

      // 绑定客户基本信息
      props.form.customerId = customerData.id;
      props.form.customerName = customerData.customerName;
      props.form.customerPhone = customerData.mobile;
      props.form.customerType = customerData.customerType;
      props.form.customerIdCode = customerData.customerIdCode;
      props.form.customerAddress = customerData.address;

      // 绑定销售相关信息
      props.form.salesOrgId = customerData.ownerOrgId;
      props.form.salesOrgName = customerData.ownerOrgName;
      props.form.salesAgentId = customerData.ownerSellerId;
      props.form.salesAgentName = customerData.ownerSellerName;

      // 设置客户找到状态，显示绿色对号图标
      customerFound.value = true;

      console.log("已绑定客户信息:", customerData);
    } else {
      // 客户不存在或查询失败，清空customerId确保表单校验失败
      customerFound.value = false;
      props.form.customerId = null;
      console.log("未找到匹配的客户信息，已清空customerId");
    }
  } catch (error) {
    console.error("查询客户ID失败:", error);
  }
};

// 获取客户定金订单数据
const fetchCustomerDepositOrders = async (customerId) => {
  try {
    console.log("开始获取客户定金订单数据，客户ID:", customerId);

    const response = await vehicleOrderApi.getCustomerDepositOrders(customerId);

    // 处理返回结果
    if (response && response.code === 200 && response.data) {
      const depositData = response.data;

      // 检查是否有定金数据
      const hasOfflineDeposit =
        depositData.offline && depositData.offline.depositTotalAmount > 0;
      const hasOnlineDeposit =
        depositData.online && depositData.online.depositTotalAmount > 0;

      if (hasOfflineDeposit || hasOnlineDeposit) {
        // 存储定金数据供联动使用
        customerDepositData.value = depositData;

        // 显示定金字段（因为查询到了定金信息）
        depositFieldsVisible.value = true;

        // 优先设置线下定金，如果没有则设置线上定金
        if (hasOfflineDeposit) {
          props.form.depositType = "offline";
          props.form.depositAmount =
            depositData.offline.depositTotalAmount / 100; // 转换为元
          console.log("已设置线下定金信息:", {
            depositType: "offline",
            depositAmount: props.form.depositAmount,
            depositOrders: depositData.offline.depositOrders,
          });
        } else if (hasOnlineDeposit) {
          props.form.depositType = "online";
          props.form.depositAmount =
            depositData.online.depositTotalAmount / 100; // 转换为元
          console.log("已设置线上定金信息:", {
            depositType: "online",
            depositAmount: props.form.depositAmount,
            depositOrders: depositData.online.depositOrders,
          });
        }

        console.log("定金数据已存储，支持类型联动，定金字段已显示:", {
          offline: hasOfflineDeposit ? depositData.offline : null,
          online: hasOnlineDeposit ? depositData.online : null,
        });
      } else {
        // 清空定金数据，保持定金字段隐藏
        customerDepositData.value = null;
        console.log("该客户暂无定金记录，定金字段保持隐藏");
      }
    } else {
      // 清空定金数据
      customerDepositData.value = null;
      console.log("获取定金订单数据失败或无数据");
    }
  } catch (error) {
    console.error("获取客户定金订单数据失败:", error);
  }
};
</script>


