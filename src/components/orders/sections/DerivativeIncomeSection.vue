<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">衍生收入</span>
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <div class="option-row has-tip">
      <span class="option-label">本单是否产生了售前衍生收入？</span>
      <n-radio-group
        v-model:value="form.hasDerivativeIncome"
        :disabled="readonly"
      >
        <n-radio-button value="NO">否</n-radio-button>
        <n-radio-button value="YES">是</n-radio-button>
      </n-radio-group>
      <span
        class="option-tip"
        v-if="form.hasDerivativeIncome === 'YES'"
        v-tip-position
        style="color: #18a058 !important"
      >
        {{
          readonly
            ? "衍生收入详情如下"
            : "请填写相关衍生收入金额，所有金额均为选填项"
        }}
      </span>
    </div>

    <!-- 当选择"是"时显示衍生收入字段 -->
    <div v-if="form.hasDerivativeIncome === 'YES'">
      <n-grid :cols="5" :x-gap="16" :y-gap="1">
        <n-grid-item>
          <n-form-item label="公证费(元)" path="notaryFee">
            <n-input-number
              v-model:value="form.notaryFee"
              :placeholder="readonly ? '' : '请输入公证费'"
              style="width: 100%"
              :precision="2"
              :min="0"
              :button-placement="readonly ? 'none' : 'both'"
              :readonly="readonly"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="畅行无忧收入(元)" path="carefreeIncome">
            <n-input-number
              v-model:value="form.carefreeIncome"
              :placeholder="readonly ? '' : '请输入畅行无忧收入'"
              style="width: 100%"
              :precision="2"
              :min="0"
              :button-placement="readonly ? 'none' : 'both'"
              :readonly="readonly"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="延保收入(元)" path="extendedWarrantyIncome">
            <n-input-number
              v-model:value="form.extendedWarrantyIncome"
              :placeholder="readonly ? '' : '请输入延保收入'"
              style="width: 100%"
              :precision="2"
              :min="0"
              :button-placement="readonly ? 'none' : 'both'"
              :readonly="readonly"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="VPS收入(元)" path="vpsIncome">
            <n-input-number
              v-model:value="form.vpsIncome"
              :placeholder="readonly ? '' : '请输入VPS收入'"
              style="width: 100%"
              :precision="2"
              :min="0"
              :button-placement="readonly ? 'none' : 'both'"
              :readonly="readonly"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="前置利息(元)" path="preInterest">
            <n-input-number
              v-model:value="form.preInterest"
              :placeholder="readonly ? '' : '请输入前置利息'"
              style="width: 100%"
              :precision="2"
              :min="0"
              :button-placement="readonly ? 'none' : 'both'"
              :readonly="readonly"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="挂牌费(元)" path="licensePlateFee">
            <n-input-number
              v-model:value="form.licensePlateFee"
              :placeholder="readonly ? '' : '请输入挂牌费'"
              style="width: 100%"
              :precision="2"
              :min="0"
              :button-placement="readonly ? 'none' : 'both'"
              :readonly="readonly"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="临牌费(元)" path="tempPlateFee">
            <n-input-number
              v-model:value="form.tempPlateFee"
              :placeholder="readonly ? '' : '请输入临牌费'"
              style="width: 100%"
              :precision="2"
              :min="0"
              :button-placement="readonly ? 'none' : 'both'"
              :readonly="readonly"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="外卖装具收入(元)" path="deliveryEquipment">
            <n-input-number
              v-model:value="form.deliveryEquipment"
              :placeholder="readonly ? '' : '请输入外卖装具收入'"
              style="width: 100%"
              :precision="2"
              :min="0"
              :button-placement="readonly ? 'none' : 'both'"
              :readonly="readonly"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="其他收入(元)" path="otherIncome">
            <n-input-number
              v-model:value="form.otherIncome"
              :placeholder="readonly ? '' : '请输入其他收入'"
              style="width: 100%"
              :precision="2"
              :min="0"
              :button-placement="readonly ? 'none' : 'both'"
              :readonly="readonly"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="售前衍生总收入(元)" path="derivativeIncomeTotal">
            <n-input-number
              :value="derivativeIncomeTotal"
              placeholder=""
              style="width: 100%"
              :precision="2"
              :min="0"
              button-placement="none"
              readonly
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from "vue";
import {
  NGrid,
  NGridItem,
  NFormItem,
  NInputNumber,
  NDivider,
  NRadioGroup,
  NRadioButton,
} from "naive-ui";

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

// 定义组件事件
const emit = defineEmits(["update:derivativeIncomeTotal"]);

// 计算衍生收入总金额
const derivativeIncomeTotal = computed(() => {
  if (props.form.hasDerivativeIncome !== "YES") {
    return 0;
  }

  // 计算所有衍生收入字段的总和
  return (
    (props.form.notaryFee || 0) +
    (props.form.carefreeIncome || 0) +
    (props.form.extendedWarrantyIncome || 0) +
    (props.form.vpsIncome || 0) +
    (props.form.preInterest || 0) +
    (props.form.licensePlateFee || 0) +
    (props.form.tempPlateFee || 0) +
    (props.form.deliveryEquipment || 0) +
    (props.form.otherIncome || 0)
  );
});

// 监听衍生收入总金额变化，通知父组件
watch(
  derivativeIncomeTotal,
  (newValue) => {
    emit("update:derivativeIncomeTotal", newValue);
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
// 样式继承自全局样式文件，无需额外定义
</style>




