<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">财务结算</span>
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <!-- 财务明细列表 -->
    <n-grid :cols="2" :x-gap="24">
      <!-- 左侧：财务明细 -->
      <n-grid-item>
        <div class="financial-details">
          <h4 class="details-title">费用明细</h4>
          <div class="details-list">
            <!-- 车辆售价 -->
            <div class="detail-item">
              <span class="item-label">车辆售价</span>
              <span class="item-amount positive">{{
                formatCurrency(form.salesAmount || 0)
              }}</span>
              <span class="item-status"></span>
            </div>

            <!-- 已付定金 -->
            <div class="detail-item" v-if="form.depositAmount > 0">
              <span class="item-label">已付定金</span>
              <span class="item-amount negative">{{
                formatCurrency(-(form.depositAmount || 0))
              }}</span>
              <span class="item-status">{{
                form.depositDeductible ? "已转车款" : "后期返现"
              }}</span>
            </div>

            <!-- 现金优惠 -->
            <div class="detail-item" v-if="form.discountAmount > 0">
              <span class="item-label">现金优惠</span>
              <span class="item-amount negative">{{
                formatCurrency(-(form.discountAmount || 0))
              }}</span>
              <span class="item-status">{{
                form.discountDeductible ? "已转车款" : "后期返现"
              }}</span>
            </div>

            <!-- 应付-客户-分期返利 -->
            <div class="detail-item" v-if="form.loanRebatePayableAmount > 0">
              <span class="item-label">应付-客户-分期返利</span>
              <span class="item-amount negative">{{
                formatCurrency(-(form.loanRebatePayableAmount || 0))
              }}</span>
              <span class="item-status">{{
                form.loanRebatePayableDeductible ? "已转车款" : "后期返现"
              }}</span>
            </div>

            <!-- 应付-客户-置换补贴 -->
            <div
              class="detail-item"
              v-if="form.usedVehicleDiscountPayableAmount > 0"
            >
              <span class="item-label">应付-客户-置换补贴</span>
              <span class="item-amount negative">{{
                formatCurrency(-(form.usedVehicleDiscountPayableAmount || 0))
              }}</span>
              <span class="item-status">{{
                form.usedVehicleDiscountPayableDeductible
                  ? "已转车款"
                  : "后期返现"
              }}</span>
            </div>

            <!-- 车辆置换金额 -->
            <div
              class="detail-item"
              v-if="form.hasUsedVehicle === 'YES' && form.usedVehicleAmount > 0"
            >
              <span class="item-label">车辆置换金额</span>
              <span class="item-amount positive">{{
                formatCurrency(form.usedVehicleAmount || 0)
              }}</span>
              <span class="item-status">置换收入</span>
            </div>

            <!-- 转车款金额 -->
            <div
              class="detail-item"
              v-if="
                form.hasUsedVehicle === 'YES' &&
                form.usedVehicleDeductibleAmount > 0
              "
            >
              <span class="item-label">转车款金额</span>
              <span class="item-amount negative">{{
                formatCurrency(-(form.usedVehicleDeductibleAmount || 0))
              }}</span>
              <span class="item-status">已转车款</span>
            </div>

            <!-- 置换差价 -->
            <div
              class="detail-item"
              v-if="form.hasUsedVehicle === 'YES' && exchangeDifference > 0"
            >
              <span class="item-label">置换差价</span>
              <span class="item-amount negative">{{
                formatCurrency(-exchangeDifference)
              }}</span>
              <span class="item-status">现金支付</span>
            </div>

            <!-- 应付-客户-专项优惠 -->
            <div
              class="detail-item"
              v-if="form.exclusiveDiscountPayableAmount > 0"
            >
              <span class="item-label">应付-客户-专项优惠</span>
              <span class="item-amount negative">{{
                formatCurrency(-(form.exclusiveDiscountPayableAmount || 0))
              }}</span>
              <span class="item-status">{{
                form.exclusiveDiscountPayableDeductible
                  ? "已转车款"
                  : "后期返现"
              }}</span>
            </div>

            <!-- 分期服务费 -->
            <div
              class="detail-item"
              v-if="form.paymentMethod === 'LOAN' && form.loanFee > 0"
            >
              <span class="item-label">分期服务费</span>
              <span class="item-amount positive">{{
                formatCurrency(form.loanFee || 0)
              }}</span>
              <span class="item-status">收入项</span>
            </div>

            <!-- 衍生收入明细 -->
            <template v-if="form.hasDerivativeIncome === 'YES'">
              <!-- 公证费 -->
              <div class="detail-item" v-if="form.notaryFee > 0">
                <span class="item-label">公证费</span>
                <span class="item-amount positive">{{
                  formatCurrency(form.notaryFee || 0)
                }}</span>
                <span class="item-status">收入项</span>
              </div>

              <!-- 畅行无忧收入 -->
              <div class="detail-item" v-if="form.carefreeIncome > 0">
                <span class="item-label">畅行无忧收入</span>
                <span class="item-amount positive">{{
                  formatCurrency(form.carefreeIncome || 0)
                }}</span>
                <span class="item-status">收入项</span>
              </div>

              <!-- 延保收入 -->
              <div class="detail-item" v-if="form.extendedWarrantyIncome > 0">
                <span class="item-label">延保收入</span>
                <span class="item-amount positive">{{
                  formatCurrency(form.extendedWarrantyIncome || 0)
                }}</span>
                <span class="item-status">收入项</span>
              </div>

              <!-- VPS收入 -->
              <div class="detail-item" v-if="form.vpsIncome > 0">
                <span class="item-label">VPS收入</span>
                <span class="item-amount positive">{{
                  formatCurrency(form.vpsIncome || 0)
                }}</span>
                <span class="item-status">收入项</span>
              </div>

              <!-- 前置利息 -->
              <div class="detail-item" v-if="form.preInterest > 0">
                <span class="item-label">前置利息</span>
                <span class="item-amount positive">{{
                  formatCurrency(form.preInterest || 0)
                }}</span>
                <span class="item-status">收入项</span>
              </div>

              <!-- 挂牌费 -->
              <div class="detail-item" v-if="form.licensePlateFee > 0">
                <span class="item-label">挂牌费</span>
                <span class="item-amount positive">{{
                  formatCurrency(form.licensePlateFee || 0)
                }}</span>
                <span class="item-status">收入项</span>
              </div>

              <!-- 临牌费 -->
              <div class="detail-item" v-if="form.tempPlateFee > 0">
                <span class="item-label">临牌费</span>
                <span class="item-amount positive">{{
                  formatCurrency(form.tempPlateFee || 0)
                }}</span>
                <span class="item-status">收入项</span>
              </div>

              <!-- 外卖装具收入 -->
              <div class="detail-item" v-if="form.deliveryEquipment > 0">
                <span class="item-label">外卖装具收入</span>
                <span class="item-amount positive">{{
                  formatCurrency(form.deliveryEquipment || 0)
                }}</span>
                <span class="item-status">收入项</span>
              </div>

              <!-- 其他收入 -->
              <div class="detail-item" v-if="form.otherIncome > 0">
                <span class="item-label">其他收入</span>
                <span class="item-amount positive">{{
                  formatCurrency(form.otherIncome || 0)
                }}</span>
                <span class="item-status">收入项</span>
              </div>
            </template>
          </div>
        </div>
      </n-grid-item>

      <!-- 右侧：汇总信息 -->
      <n-grid-item>
        <div class="financial-summary">
          <h4 class="summary-title">费用汇总</h4>

          <!-- 计算说明 -->
          <div class="calculation-note">
            <small style="color: #666; font-size: 12px; line-height: 1.4">
              <strong>计算说明：</strong><br />
              • 预计应收总额 = 成交价格 + 全部收入项<br />
              • 成交价格已扣除所有转车款项目<br />
              <span v-if="form.paymentMethod === 'LOAN'">
                • 预计应收客户 = 首付金额 + 收入项<br />
              </span>
              <span v-else> • 预计应收客户 = 预计应收总额 </span>
            </small>
          </div>

          <!-- 预计应收总额 -->
          <div class="summary-item">
            <span class="summary-label">预计应收总额</span>
            <span
              class="summary-value"
              :class="getSummaryValueClass(totalReceivableAmount, 'positive')"
              >{{ formatCurrency(totalReceivableAmount) }}</span
            >
          </div>

          <!-- 衍生总收入 -->
          <div class="summary-item" v-if="derivativeIncomeTotal > 0">
            <span class="summary-label">衍生总收入</span>
            <span
              class="summary-value"
              :class="getSummaryValueClass(derivativeIncomeTotal, 'positive')"
              >{{ formatCurrency(derivativeIncomeTotal) }}</span
            >
          </div>

          <!-- 已折现合计 -->
          <div class="summary-item">
            <span class="summary-label">已折现合计</span>
            <span
              class="summary-value"
              :class="getSummaryValueClass(totalDiscountedAmount, 'negative')"
              >{{ formatCurrency(-totalDiscountedAmount) }}</span
            >
          </div>

          <!-- 预计后期返客户 -->
          <div class="summary-item">
            <span class="summary-label">预计后期返客户</span>
            <span
              class="summary-value"
              :class="getSummaryValueClass(totalPayableToCustomer, 'negative')"
              >{{ formatCurrency(totalPayableToCustomer) }}</span
            >
          </div>

          <!-- 预计应收客户 -->
          <div class="summary-item main-total">
            <span class="summary-label">预计应收客户</span>
            <span
              class="summary-value"
              :class="getSummaryValueClass(finalPrice, 'main')"
              >{{ formatCurrency(finalPrice) }}</span
            >
          </div>
        </div>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { computed } from "vue";

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    default: () => ({
      visible: true,
      editable: true,
      fields: {},
    }),
  },
});

// 计算预计应收客户
const finalPrice = computed(() => {
  let result = 0;

  if (props.form.paymentMethod === "LOAN") {
    // 分期付款：预计应收客户 = 首付金额 + 所有收入项
    const loanInitialAmount = props.form.loanInitialAmount || 0;
    const loanFee = props.form.loanFee || 0;
    const derivativeIncome = derivativeIncomeTotal.value;

    result = loanInitialAmount + loanFee + derivativeIncome;

    console.log("预计应收客户计算(分期):", {
      首付金额: loanInitialAmount,
      分期服务费: loanFee,
      衍生收入: derivativeIncome,
      预计应收客户: result,
    });
  } else {
    // 全款付款：预计应收客户 = 预计应收总额（成交金额已扣除转车款）
    const receivableTotal = totalReceivableAmount.value;
    result = receivableTotal;

    console.log("预计应收客户计算(全款):", {
      预计应收总额: receivableTotal,
      预计应收客户: result,
    });
  }

  return result;
});

// 计算置换差价
const exchangeDifference = computed(() => {
  if (props.form.hasUsedVehicle === "YES") {
    const usedVehicleAmount = props.form.usedVehicleAmount || 0;
    const usedVehicleDeductibleAmount =
      props.form.usedVehicleDeductibleAmount || 0;
    return Math.max(0, usedVehicleAmount - usedVehicleDeductibleAmount);
  }
  return 0;
});

// 计算衍生收入总金额
const derivativeIncomeTotal = computed(() => {
  if (props.form.hasDerivativeIncome !== "YES") {
    return 0;
  }

  return (
    (props.form.notaryFee || 0) +
    (props.form.carefreeIncome || 0) +
    (props.form.extendedWarrantyIncome || 0) +
    (props.form.vpsIncome || 0) +
    (props.form.preInterest || 0) +
    (props.form.licensePlateFee || 0) +
    (props.form.tempPlateFee || 0) +
    (props.form.deliveryEquipment || 0) +
    (props.form.otherIncome || 0)
  );
});

// 计算预计应收总额（收入项合计）
const totalReceivableAmount = computed(() => {
  // 预计应收总额 = 成交价 + 全部收入项
  let total = props.form.dealAmount || 0;

  // 加上分期服务费
  if (props.form.paymentMethod === "LOAN" && props.form.loanFee > 0) {
    total += props.form.loanFee;
  }

  // 加上衍生收入总金额
  total += derivativeIncomeTotal.value;

  return total;
});

// 计算已折现合计（所有已转车款选项合计）
const totalDiscountedAmount = computed(() => {
  let total = 0;

  // 已付定金（已转车款）
  if (props.form.depositAmount > 0 && props.form.depositDeductible) {
    total += props.form.depositAmount;
  }

  // 现金优惠（已转车款）
  if (props.form.discountAmount > 0 && props.form.discountDeductible) {
    total += props.form.discountAmount;
  }

  // 应付-客户-分期返利（已转车款）
  if (
    props.form.loanRebatePayableAmount > 0 &&
    props.form.loanRebatePayableDeductible
  ) {
    total += props.form.loanRebatePayableAmount;
  }

  // 应付-客户-置换补贴（已转车款）
  if (
    props.form.usedVehicleDiscountPayableAmount > 0 &&
    props.form.usedVehicleDiscountPayableDeductible
  ) {
    total += props.form.usedVehicleDiscountPayableAmount;
  }

  // 置换转车款
  if (
    props.form.hasUsedVehicle === "YES" &&
    props.form.usedVehicleDeductibleAmount > 0
  ) {
    total += props.form.usedVehicleDeductibleAmount;
  }

  // 应付-客户-专项优惠（已转车款）
  if (
    props.form.exclusiveDiscountPayableAmount > 0 &&
    props.form.exclusiveDiscountPayableDeductible
  ) {
    total += props.form.exclusiveDiscountPayableAmount;
  }

  return total;
});

// 计算预计后期返客户（所有未转车款选项合计）
const totalPayableToCustomer = computed(() => {
  let total = 0;

  // 已付定金（未转车款）
  if (props.form.depositAmount > 0 && !props.form.depositDeductible) {
    total += props.form.depositAmount;
  }

  // 现金优惠（未转车款）
  if (props.form.discountAmount > 0 && !props.form.discountDeductible) {
    total += props.form.discountAmount;
  }

  // 应付-客户-分期返利（未转车款）
  if (
    props.form.loanRebatePayableAmount > 0 &&
    !props.form.loanRebatePayableDeductible
  ) {
    total += props.form.loanRebatePayableAmount;
  }

  // 应付-客户-置换补贴（未转车款）
  if (
    props.form.usedVehicleDiscountPayableAmount > 0 &&
    !props.form.usedVehicleDiscountPayableDeductible
  ) {
    total += props.form.usedVehicleDiscountPayableAmount;
  }

  // 应付-客户-专项优惠（未转车款）
  if (
    props.form.exclusiveDiscountPayableAmount > 0 &&
    !props.form.exclusiveDiscountPayableDeductible
  ) {
    total += props.form.exclusiveDiscountPayableAmount;
  }

  // 置换差价（现金支付）
  total += exchangeDifference.value;

  return total;
});

// 格式化货币
const formatCurrency = (value) => {
  if (value === undefined || value === null || value === 0) return "¥0.00";
  return `¥${value.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
};

// 获取汇总金额的CSS类
const getSummaryValueClass = (value, type) => {
  // 如果金额为0，返回灰色类
  if (value === 0 || value === null || value === undefined) {
    return "zero";
  }

  // 根据类型返回相应的颜色类
  switch (type) {
    case "positive":
      return "positive";
    case "negative":
      return "negative";
    case "main":
      return ""; // 主要金额使用默认样式
    default:
      return "";
  }
};
</script>

<style lang="scss" scoped>
.financial-details {
  .details-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .details-list {
    .detail-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .item-label {
        flex: 1;
        font-size: 14px;
        color: #666;
      }

      .item-amount {
        min-width: 100px;
        text-align: right;
        font-size: 14px;
        font-weight: 500;
        margin-right: 12px;

        &.positive {
          color: #52c41a;
        }

        &.negative {
          color: #ff4d4f;
        }
      }

      .item-status {
        min-width: 80px;
        text-align: center;
        font-size: 12px;
        color: #999;
        padding: 2px 8px;
        border-radius: 4px;
        background-color: #f5f5f5;

        &:empty {
          background-color: transparent;
        }
      }
    }
  }
}

.financial-summary {
  .summary-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .calculation-note {
    padding: 12px;
    margin-bottom: 16px;
    background-color: #f0f9ff;
    border: 1px solid #bae7ff;
    border-radius: 6px;
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 8px;
    background-color: #fafafa;
    border-radius: 6px;

    &.main-total {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
      font-weight: 600;
    }

    .summary-label {
      font-size: 14px;
      color: #666;
    }

    .summary-value {
      font-size: 16px;
      font-weight: 500;

      &.positive {
        color: #52c41a;
      }

      &.negative {
        color: #ff4d4f;
      }

      &.zero {
        color: #999;
      }
    }

    &.main-total .summary-value {
      color: #1890ff;
      font-size: 18px;
    }
  }
}
</style>
