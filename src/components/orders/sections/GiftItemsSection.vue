<template>
  <div v-if="sectionConfig.visible" class="section-container">
    <div class="section-title">
      <span class="title-text">赠品明细</span>
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <div class="option-row has-tip">
      <span class="option-label">本单是否存在赠送商品或服务？</span>
      <n-radio-group
        v-model:value="form.hasGiftItems"
        :disabled="!isFieldEditable('hasGiftItems')"
      >
        <n-radio-button value="NO">否</n-radio-button>
        <n-radio-button value="YES">是</n-radio-button>
      </n-radio-group>
      <span
        class="option-tip"
        v-if="form.hasGiftItems === 'YES'"
        v-tip-position
        style="color: #18a058 !important"
      >
        点击➕图标选择赠送商品或服务
      </span>
    </div>

    <!-- 当选择"是"时显示赠品明细表格 -->
    <n-grid
      v-if="form.hasGiftItems === 'YES' && isFieldVisible('giftItems')"
      :cols="4"
      :x-gap="16"
      :y-gap="1"
    >
      <n-grid-item span="4">
        <gift-items-table
          v-model="form.giftItems"
          ref="giftItemsTableRef"
          :disabled="!isFieldEditable('giftItems')"
          :form="form"
        />
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import {
  NGrid,
  NGridItem,
  NDivider,
  NRadioGroup,
  NRadioButton,
} from "naive-ui";
import GiftItemsTable from "@/components/orders/GiftItemsTable.vue";

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    default: () => ({
      visible: true,
      editable: true,
      fields: {},
    }),
  },
});

// 计算属性：获取section配置
const sectionConfig = computed(() => {
  return (
    props.config || {
      visible: true,
      editable: true,
      fields: {},
    }
  );
});

// 工具函数：检查字段是否可见
const isFieldVisible = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  return fieldConfig?.visible !== false; // 默认可见
};

// 工具函数：检查字段是否可编辑
const isFieldEditable = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  const sectionEditable = sectionConfig.value.editable !== false;
  const fieldEditable = fieldConfig?.editable !== false;
  return sectionEditable && fieldEditable; // 区域和字段都必须可编辑
};

// 表格引用
const giftItemsTableRef = ref(null);

// 暴露方法给父组件
defineExpose({
  giftItemsTableRef,
});
</script>


