<template>
  <div v-if="sectionConfig.visible" class="section-container">
    <div class="section-title">
      <span class="title-text">车辆保险</span>
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <div class="option-row has-tip">
      <span class="option-label">客户是否在我司购置了车辆保险？</span>
      <n-radio-group
        v-model:value="form.hasInsurance"
        :disabled="!isFieldEditable('hasInsurance')"
      >
        <n-radio-button value="NO">否</n-radio-button>
        <n-radio-button value="YES">是</n-radio-button>
      </n-radio-group>
      <span
        class="option-tip"
        v-if="form.hasInsurance === 'YES'"
        v-tip-position
        style="color: #18a058 !important"
      >
        客户已选择购买车辆保险，系统将推送订单信息给指定经办机构的保险专员处理
      </span>
    </div>

    <!-- 当选择"是"时显示经办机构选择 -->
    <n-grid v-if="form.hasInsurance === 'YES'" :cols="3" :x-gap="16" :y-gap="6">
      <n-grid-item v-if="isFieldVisible('insuranceOrgName')">
        <n-form-item label="经办机构" path="insuranceOrgName" required>
          <n-input
            v-model:value="form.insuranceOrgName"
            placeholder="请选择保险经办机构"
            readonly
            @click="handleInsuranceOrgClick"
          >
            <template #suffix>
              <n-button
                v-if="isFieldEditable('insuranceOrgName')"
                quaternary
                circle
                @click.stop="handleInsuranceOrgClick"
              >
                <template #icon>
                  <n-icon class="add-icon">
                    <component :is="AddOutlineIcon" />
                  </n-icon>
                </template>
              </n-button>
            </template>
          </n-input>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <!-- 空白占位 -->
      </n-grid-item>
      <n-grid-item>
        <!-- 空白占位 -->
      </n-grid-item>
    </n-grid>

    <!-- 业务机构选择器 -->
    <BizOrgSelector
      :visible="showBizOrgSelector"
      @update:visible="showBizOrgSelector = $event"
      :single="true"
      title="选择保险经办机构"
      @select="handleInsuranceOrgSelect"
      @cancel="handleInsuranceOrgCancel"
      :listAll="true"
    />
  </div>
</template>

<script setup>
import { ref, computed, markRaw } from "vue";
import {
  NDivider,
  NRadioGroup,
  NRadioButton,
  NGrid,
  NGridItem,
  NFormItem,
  NInput,
  NButton,
  NIcon,
} from "naive-ui";
import { AddOutline } from "@vicons/ionicons5";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const AddOutlineIcon = markRaw(AddOutline);

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    default: () => ({
      visible: true,
      editable: true,
      fields: {},
    }),
  },
});

// 计算属性：获取section配置
const sectionConfig = computed(() => {
  return (
    props.config || {
      visible: true,
      editable: true,
      fields: {},
    }
  );
});

// 工具函数：检查字段是否可见
const isFieldVisible = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  return fieldConfig?.visible !== false; // 默认可见
};

// 工具函数：检查字段是否可编辑
const isFieldEditable = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  const sectionEditable = sectionConfig.value.editable !== false;
  const fieldEditable = fieldConfig?.editable !== false;
  return sectionEditable && fieldEditable; // 区域和字段都必须可编辑
};

// 定义组件事件
const emit = defineEmits(["update:form"]);

// 组件状态
const showBizOrgSelector = ref(false);

// 处理保险经办机构选择器点击
const handleInsuranceOrgClick = () => {
  showBizOrgSelector.value = true;
};

// 处理保险经办机构选择
const handleInsuranceOrgSelect = (orgs) => {
  if (orgs && orgs.length > 0) {
    const selectedOrg = orgs[0]; // 因为是单选模式，所以取第一个

    // 更新表单数据
    const updatedForm = {
      ...props.form,
      insuranceOrgName: selectedOrg.orgName || selectedOrg.name || "",
      insuranceOrgId: selectedOrg.id,
    };

    emit("update:form", updatedForm);

    // 关闭选择器
    showBizOrgSelector.value = false;
  }
};

// 处理保险经办机构选择取消
const handleInsuranceOrgCancel = () => {
  showBizOrgSelector.value = false;
};
</script>

<style lang="scss" scoped>
:deep(.n-input) {
  cursor: pointer;
}

:deep(.n-button) {
  margin-right: -4px;
}

.add-icon {
  color: var(--primary-color, #18a058);
  font-size: 18px;
}
</style>


