<template>
  <div v-if="sectionConfig.visible" class="section-container">
    <div class="section-title">
      <span class="title-text">付款方式</span>
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <div class="option-row has-tip">
      <span class="option-label">请选择客户的付款方式：</span>
      <n-radio-group
        v-model:value="form.paymentMethod"
        :disabled="!isFieldEditable('paymentMethod')"
      >
        <n-radio-button value="FULL">全款</n-radio-button>
        <n-radio-button value="LOAN">分期</n-radio-button>
      </n-radio-group>
    </div>

    <!-- 分期付款提示信息 -->
    <div v-if="form.paymentMethod === 'LOAN'" class="loan-info-tip">
      <n-alert type="info" style="margin-bottom: 16px">
        <template #header>分期付款说明</template>
        分期金额计算基于成交金额（已扣除所有转车款）：{{
          formatMoney(form.dealAmount || 0)
        }}， 首付金额 + 分期金额 = 成交金额
      </n-alert>
    </div>

    <!-- 贷款信息，仅在选择贷款时显示 -->
    <n-grid
      v-if="form.paymentMethod === 'LOAN'"
      :cols="4"
      :x-gap="16"
      :y-gap="1"
    >
      <n-grid-item v-if="isFieldVisible('loanChannel')">
        <n-form-item label="分期金融机构" path="loanChannel" required>
          <n-select
            v-model:value="form.loanChannel"
            :options="loanChannelOptions"
            placeholder="请选择分期金融机构"
            :disabled="!isFieldEditable('loanChannel')"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('loanAmount')">
        <n-form-item label="分期金额(元)" path="loanAmount" required>
          <n-input-number
            v-model:value="form.loanAmount"
            placeholder="请输入贷款金额"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
            @update:value="handleLoanAmountChange"
            :disabled="!isFieldEditable('loanAmount')"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('loanInitialAmount')">
        <n-form-item label="首付金额(元)" path="loanInitialAmount" required>
          <n-input-number
            v-model:value="form.loanInitialAmount"
            placeholder="请输入首付金额"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
            @update:value="handleLoanInitialAmountChange"
            :disabled="!isFieldEditable('loanInitialAmount')"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="首付比例(%)" path="loanInitialRatio">
          <n-input-number
            v-model:value="form.loanInitialRatio"
            disabled
            style="width: 100%"
            :precision="2"
            :show-button="false"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="分期时长" path="loanMonths" required>
          <n-select
            v-model:value="form.loanMonths"
            :options="loanMonthsOptions"
            placeholder="请选择分期期数"
            clearable
            :disabled="!isFieldEditable('loanMonths')"
          />
        </n-form-item>
      </n-grid-item>

      <!-- 第二行开始 -->
      <n-grid-item>
        <n-form-item label="分期服务费(元)" path="loanFee">
          <n-input-number
            v-model:value="form.loanFee"
            placeholder="请输入分期服务费"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
            @update:value="handleLoanFeeChange"
            :disabled="!isFieldEditable('loanFee')"
          />
        </n-form-item>
      </n-grid-item>

      <n-grid-item v-if="showLoanRebateReceivable">
        <n-form-item
          label="应收-机构-分期返利(元)"
          path="loanRebateReceivableAmount"
          :rule="loanRebateReceivableRule"
        >
          <div style="display: flex; align-items: center; width: 100%">
            <n-input-number
              v-model:value="form.loanRebateReceivableAmount"
              placeholder="应收机构分期返利"
              style="flex: 1"
              :precision="2"
              :min="0"
              button-placement="both"
              :disabled="!isFieldEditable('loanRebateReceivableAmount')"
            />
          </div>
        </n-form-item>
      </n-grid-item>

      <n-grid-item>
        <n-form-item
          label="应付-客户-分期返利(元)"
          path="loanRebatePayableAmount"
        >
          <div style="display: flex; align-items: center; width: 100%">
            <n-input-number
              v-model:value="form.loanRebatePayableAmount"
              placeholder="应付客户分期返利"
              style="flex: 1"
              :precision="2"
              :min="0"
              button-placement="both"
              @update:value="handleLoanRebatePayableAmountChange"
              :disabled="!isFieldEditable('loanRebatePayableAmount')"
            />
            <n-checkbox
              v-model:checked="form.loanRebatePayableDeductible"
              style="margin-left: 10px; white-space: nowrap; width: 80px"
              @update:checked="handleLoanRebatePayableDeductibleChange"
              :disabled="!isFieldEditable('loanRebatePayableDeductible')"
              >转车款</n-checkbox
            >
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { computed } from "vue";
import {
  NGrid,
  NGridItem,
  NFormItem,
  NInputNumber,
  NDivider,
  NRadioGroup,
  NRadioButton,
  NSelect,
  NCheckbox,
  NAlert,
} from "naive-ui";
import { formatMoney } from "@/utils/money";

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  loanChannelOptions: {
    type: Array,
    default: () => [],
  },
  loanMonthsOptions: {
    type: Array,
    default: () => [],
  },
  showLoanRebateReceivable: {
    type: Boolean,
    default: true,
  },
  config: {
    type: Object,
    default: () => ({
      visible: true,
      editable: true,
      fields: {},
    }),
  },
});

// 计算属性：获取section配置
const sectionConfig = computed(() => {
  const config = props.config || {
    visible: true,
    editable: true,
    fields: {},
  };

  // 调试输出
  console.log("PaymentMethodSection 配置:", config);

  return config;
});

// 工具函数：检查字段是否可见
const isFieldVisible = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  return fieldConfig?.visible !== false; // 默认可见
};

// 工具函数：检查字段是否可编辑
const isFieldEditable = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];

  let result;
  // 如果字段有明确的可编辑性配置，优先使用字段配置
  if (fieldConfig && fieldConfig.editable !== undefined) {
    result = fieldConfig.editable;
  } else {
    // 否则使用区域级别的配置，默认为可编辑
    result = sectionConfig.value.editable !== false;
  }

  // 调试输出
  console.log(`字段 ${fieldName} 可编辑性:`, {
    fieldConfig,
    sectionEditable: sectionConfig.value.editable,
    result,
  });

  return result;
};

// 定义组件事件
const emit = defineEmits([
  "handle-loan-amount-change",
  "handle-loan-initial-amount-change",
  "handle-loan-fee-change",
  "handle-loan-rebate-payable-amount-change",
  "handle-loan-rebate-payable-deductible-change",
]);

// 应收机构分期返利验证规则
const loanRebateReceivableRule = computed(() => {
  // 只有当字段显示且付款方式为分期时才进行必填验证
  if (props.showLoanRebateReceivable && props.form.paymentMethod === "LOAN") {
    return {
      required: true,
      type: "number",
      message: "请输入应收机构分期返利金额",
      trigger: ["blur", "change"],
      validator: (_, value) => {
        if (value === null || value === undefined || value === "") {
          return new Error("请输入应收机构分期返利金额");
        }
        if (value < 0) {
          return new Error("应收机构分期返利金额不能为负数");
        }
        return true;
      },
    };
  }
  // 不显示时不进行验证
  return null;
});

// 处理贷款金额变化
const handleLoanAmountChange = () => {
  emit("handle-loan-amount-change");
};

// 处理首付金额变化
const handleLoanInitialAmountChange = () => {
  emit("handle-loan-initial-amount-change");
};

// 处理分期服务费变化
const handleLoanFeeChange = () => {
  emit("handle-loan-fee-change");
};

// 处理应付客户分期返利金额变化
const handleLoanRebatePayableAmountChange = () => {
  emit("handle-loan-rebate-payable-amount-change");
};

// 处理应付客户分期返利转车款选项变化
const handleLoanRebatePayableDeductibleChange = () => {
  emit("handle-loan-rebate-payable-deductible-change");
};
</script>

<style scoped>
/* 修复第一个按钮的左边框问题 */
:deep(.n-radio-group .n-radio-button:first-child) {
  border-left: 1px solid var(--border-color) !important;
}

:deep(
    .n-radio-group
      .n-radio-button:first-child:hover:not(.n-radio-button--checked)
  ) {
  border-left: 1px solid var(--primary-color) !important;
  z-index: 10;
}

/* 修复最后一个按钮的右边框问题 */
:deep(.n-radio-group .n-radio-button:last-child) {
  border-right: 1px solid var(--border-color) !important;
}

:deep(
    .n-radio-group
      .n-radio-button:last-child:hover:not(.n-radio-button--checked)
  ) {
  border-right: 1px solid var(--primary-color) !important;
  z-index: 10;
}

/* 确保选中状态没有边框 */
:deep(.n-radio-button--checked) {
  border: none !important;
}

:deep(.n-radio-button--checked:hover) {
  border: none !important;
}
</style>



