<template>
  <div v-if="sectionConfig.visible" class="section-container">
    <div class="section-title">
      <span class="title-text">车辆置换</span>
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <div class="option-row has-tip">
      <span class="option-label">客户是否有车辆置换？</span>
      <n-radio-group
        v-model:value="form.hasUsedVehicle"
        :disabled="!isFieldEditable('hasUsedVehicle')"
      >
        <n-radio-button value="NO">否</n-radio-button>
        <n-radio-button value="YES">是</n-radio-button>
      </n-radio-group>
      <span
        class="option-tip"
        v-if="form.hasUsedVehicle === 'YES'"
        v-tip-position
        style="color: #18a058 !important"
      >
        请填写置换车辆信息，车牌号和VIN为必填项。
      </span>
    </div>

    <!-- 当选择"有"时显示二手车置换信息 -->
    <n-grid
      v-if="form.hasUsedVehicle === 'YES'"
      :cols="4"
      :x-gap="16"
      :y-gap="1"
    >
      <n-grid-item>
        <n-form-item label="置换车牌号" path="usedVehicleId" required>
          <n-input
            v-model:value="form.usedVehicleId"
            placeholder="请输入置换车牌号"
            :disabled="!isFieldEditable('usedVehicleId')"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="置换车VIN" path="usedVehicleVin" required>
          <n-input
            v-model:value="form.usedVehicleVin"
            placeholder="请输入17位车辆VIN"
            :disabled="!isFieldEditable('usedVehicleVin')"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="置换车品牌" path="usedVehicleBrand">
          <n-input
            v-model:value="form.usedVehicleBrand"
            placeholder="请输入置换车品牌"
            :disabled="!isFieldEditable('usedVehicleBrand')"
            maxlength="20"
            show-count
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="置换车型" path="usedVehicleModel">
          <n-input
            v-model:value="form.usedVehicleModel"
            placeholder="请输入置换车型"
            :disabled="!isFieldEditable('usedVehicleModel')"
            maxlength="20"
            show-count
          />
        </n-form-item>
      </n-grid-item>

      <n-grid-item>
        <n-form-item label="置换金额(元)" path="usedVehicleAmount">
          <n-input-number
            v-model:value="form.usedVehicleAmount"
            placeholder="请输入置换金额"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
            :disabled="!isFieldEditable('usedVehicleAmount')"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="转车款金额(元)" path="usedVehicleDeductibleAmount">
          <n-input-number
            v-model:value="form.usedVehicleDeductibleAmount"
            placeholder="抵扣车款金额"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
            @update:value="handleUsedVehicleDeductibleAmountChange"
            :disabled="!isFieldEditable('usedVehicleDeductibleAmount')"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item
          label="应收-厂家-置换补贴(元)"
          path="usedVehicleDiscountReceivableAmount"
        >
          <n-input-number
            v-model:value="form.usedVehicleDiscountReceivableAmount"
            placeholder="应收厂家置换补贴"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
            :disabled="!isFieldEditable('usedVehicleDiscountReceivableAmount')"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item
          label="应付-客户-置换补贴(元)"
          path="usedVehicleDiscountPayableAmount"
        >
          <div style="display: flex; align-items: center; width: 100%">
            <n-input-number
              v-model:value="form.usedVehicleDiscountPayableAmount"
              placeholder="应付客户置换补贴"
              style="flex: 1"
              :precision="2"
              :min="0"
              button-placement="both"
              @update:value="handleUsedVehicleDiscountPayableAmountChange"
              :disabled="!isFieldEditable('usedVehicleDiscountPayableAmount')"
            />
            <n-checkbox
              v-model:checked="form.usedVehicleDiscountPayableDeductible"
              style="margin-left: 10px; white-space: nowrap; width: 80px"
              @update:checked="handleUsedVehicleDiscountPayableDeductibleChange"
              :disabled="
                !isFieldEditable('usedVehicleDiscountPayableDeductible')
              "
              >转车款</n-checkbox
            >
          </div>
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { computed } from "vue";
import {
  NGrid,
  NGridItem,
  NFormItem,
  NInput,
  NInputNumber,
  NDivider,
  NRadioGroup,
  NRadioButton,
  NCheckbox,
} from "naive-ui";

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    default: () => ({
      visible: true,
      editable: true,
      fields: {},
    }),
  },
});

// 计算属性：获取section配置
const sectionConfig = computed(() => {
  return (
    props.config || {
      visible: true,
      editable: true,
      fields: {},
    }
  );
});

// 工具函数：检查字段是否可见
const isFieldVisible = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  return fieldConfig?.visible !== false; // 默认可见
};

// 工具函数：检查字段是否可编辑
const isFieldEditable = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];

  // 如果字段有明确的可编辑性配置，优先使用字段配置
  if (fieldConfig && fieldConfig.editable !== undefined) {
    return fieldConfig.editable;
  }

  // 否则使用区域级别的配置，默认为可编辑
  return sectionConfig.value.editable !== false;
};

// 定义组件事件
const emit = defineEmits([
  "handle-used-vehicle-deductible-amount-change",
  "handle-used-vehicle-discount-payable-amount-change",
  "handle-used-vehicle-discount-payable-deductible-change",
]);

// 处理转车款金额变化
const handleUsedVehicleDeductibleAmountChange = () => {
  emit("handle-used-vehicle-deductible-amount-change");
};

// 处理应付客户置换补贴金额变化
const handleUsedVehicleDiscountPayableAmountChange = () => {
  emit("handle-used-vehicle-discount-payable-amount-change");
};

// 处理应付客户置换补贴转车款选项变化
const handleUsedVehicleDiscountPayableDeductibleChange = () => {
  emit("handle-used-vehicle-discount-payable-deductible-change");
};
</script>



