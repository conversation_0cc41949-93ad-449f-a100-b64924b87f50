/**
 * 订单表单逻辑组合式函数
 */
import { ref, reactive, computed, watch } from 'vue'
import { createDefaultOrderForm, mergeInitialData } from '@/utils/orderFormFactory'
import { getOrderFormRules } from '@/utils/orderFormRules'
import {
  calculateFinalPrice,
  calculateProfitRate,
  handleLoanAmountChange,
  handleLoanInitialAmountChange,
  convertFormToApiData
} from '@/utils/orderFormUtils'
import { convertToWanYuan } from '@/utils/money'
import messages from '@/utils/messages'
import vehicleOrderApi from '@/api/vehicleOrder'

/**
 * 订单表单逻辑
 * @param {Object} props - 组件属性
 * @param {Function} emit - 事件发射函数
 * @returns {Object} 表单相关的状态和方法
 */
export function useOrderForm(props, emit) {
  // 表单引用
  const formRef = ref(null)

  // 选择器状态
  const customerSelectorVisible = ref(false)
  const vehicleSelectorVisible = ref(false)
  const selectedOutboundOrg = ref(null)

  // 表单数据
  const form = reactive(createDefaultOrderForm())

  // 如果有初始数据，合并到表单
  if (props.initialData) {
    mergeInitialData(form, props.initialData)
  }

  // 表单验证规则
  const rules = computed(() => getOrderFormRules(form))

  // 计算属性：成交价格（万元）
  const finalPriceInWan = computed(() => {
    return convertToWanYuan(form.dealAmount)
  })

  // 贷款渠道选项
  const loanChannelOptions = [
    { label: '车贷通担保', value: '车贷通担保' },
    { label: '建行', value: '建行' },
    { label: '长安金融', value: '长安金融' },
    { label: '上汽金融', value: '上汽金融' },
    { label: '其他', value: '其他' }
  ]

  // 贷款期限选项
  const loanMonthsOptions = [
    { label: '3期', value: 3 },
    { label: '6期', value: 6 },
    { label: '12期', value: 12 },
    { label: '24期', value: 24 },
    { label: '36期', value: 36 },
    { label: '60期', value: 60 }
  ]

  // 显示客户选择器
  const showCustomerSelector = () => {
    customerSelectorVisible.value = true
  }

  // 显示车辆选择器
  const showVehicleSelector = () => {
    vehicleSelectorVisible.value = true
  }

  // 处理客户选择
  const handleCustomerSelected = (customer) => {
    messages.success(`已选择客户: ${customer.customerName}`)

    // 更新客户信息
    form.customerId = customer.id
    form.customerName = customer.customerName
    form.customerType = customer.customerType || 'individual' // 客户类型，默认为个人客户
    form.customerPhone = customer.mobile
    form.customerAddress = customer.address
    form.salesAgentName = customer.ownerSellerName

    // 设置销售顾问和销售主管ID
    form.salesAgentId = customer.ownerSellerId || customer.id
    form.salesLeaderId = customer.ownerLeaderId || customer.id

    // 如果是新增，也更新销售单位
    if (!props.isEdit) {
      form.salesOrgId = customer.ownerOrgId || null
      form.salesOrgName = customer.ownerOrgName || ''
    }
  }

  // 处理车辆选择
  const handleVehicleSelected = (vehicle) => {
    if (!vehicle) return

    messages.success(`已选择车型: ${vehicle.brand} ${vehicle.series}`)

    // 更新车辆信息
    form.skuId = vehicle.id
    form.vehicleBrand = vehicle.brand || ''
    form.vehicleSeries = vehicle.series || ''
    form.vehicleConfig = vehicle.configName || ''
    form.vehicleColorCode = vehicle.colorCode || ''
    form.sbAmount = vehicle.sbPrice || 0

    // 默认销售价为启票价的1.35倍
    form.salesAmount = parseFloat((form.sbAmount * 1.35).toFixed(2))

    // 更新金额计算
    calculateFinalPrice(form)
    calculateProfitRate(form)
  }

  // 处理出库单位选择
  const handleOutboundOrgChange = (org) => {
    if (org) {
      form.deliveryOrgId = org.id
      form.outboundOrgName = org.name
      selectedOutboundOrg.value = org
    } else {
      form.deliveryOrgId = null
      form.outboundOrgName = ''
      selectedOutboundOrg.value = null
    }
  }

  // 处理销售价格变化
  const handleSalePriceChange = () => {
    calculateFinalPrice(form)
    calculateProfitRate(form)
  }

  // 处理优惠金额变化
  const handleDiscountChange = () => {
    calculateFinalPrice(form)
    calculateProfitRate(form)
  }



  // 处理贷款金额变化
  const handleLoanAmountChange = () => {
    handleLoanAmountChange(form)
  }

  // 处理首付金额变化
  const handleLoanInitialAmountChange = () => {
    handleLoanInitialAmountChange(form)
  }

  // 处理分期服务费变化
  const handleLoanFeeChange = () => {
    // 分期服务费是独立的收入项，不影响成交价格和分期金额计算
    // 因此这里不需要重新计算成交价格和分期相关金额
    // 只需要重新计算毛利率（因为分期服务费会影响总收入）
    calculateProfitRate(form)
  }

  // 处理保存
  const handleSave = () => {
    // 如果是分期付款方式，确保分期期数有值
    if (form.paymentMethod === 'LOAN' && (form.loanMonths === null || form.loanMonths === undefined || form.loanMonths === '')) {
      form.loanMonths = 12 // 默认设置为12期
      console.log('保存前自动设置分期期数为12期')
    }

    // 执行表单验证
    formRef.value?.validate(async (errors, _) => {
      if (errors) {
        console.error('表单验证错误:', errors)
        return messages.error('请完善表单信息')
      }

      try {
        // 构建API请求数据
        const orderPayload = convertFormToApiData(form)

        // 调用保存API
        const response = props.isEdit
          ? await vehicleOrderApi.updateOrder(form.id, orderPayload)
          : await vehicleOrderApi.createOrder(orderPayload)

        if (response.code === 200) {
          messages.success(props.isEdit ? '订单更新成功' : '订单创建成功')
          emit('save', response.data)
          emit('update:visible', false)
        } else {
          messages.error(response.message || (props.isEdit ? '订单更新失败' : '订单创建失败'))
        }
      } catch (error) {
        console.error(props.isEdit ? '更新订单失败，请稍后重试' : '创建订单失败，请稍后重试')
      }
    })
  }

  // 处理取消
  const handleCancel = () => {
    emit('update:visible', false)
    emit('cancel')
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(form, createDefaultOrderForm())
    selectedOutboundOrg.value = null
  }

  // 监听车辆信息变化，自动计算金额
  watch([() => form.sbAmount, () => form.salesAmount], () => {
    calculateFinalPrice(form)
    calculateProfitRate(form)
  })

  // 监听车辆销售价格变化，更新贷款相关金额
  // 注意：贷款金额应该基于车辆销售价格，而不是成交总价
  // 因为分期服务费等其他费用是独立的收入科目，不应影响贷款计算
  watch(() => form.salesAmount, (newValue, oldValue) => {
    if (newValue > 0 && form.paymentMethod === 'LOAN') {
      // 如果车辆销售价格变化，且付款方式为贷款
      if (oldValue > 0 && form.loanInitialRatio > 0) {
        // 如果之前有值，保持首付比例不变，重新计算首付金额和贷款金额
        const ratio = form.loanInitialRatio / 100
        // 首付金额 = 车辆销售价格 * 首付比例
        form.loanInitialAmount = parseFloat((newValue * ratio).toFixed(2))
        // 贷款金额 = 车辆销售价格 - 首付金额
        form.loanAmount = parseFloat((newValue - form.loanInitialAmount).toFixed(2))
      } else {
        // 如果之前没有值，设置默认值：首付30%，贷款70%
        form.loanInitialRatio = 30
        form.loanInitialAmount = parseFloat((newValue * 0.3).toFixed(2))
        form.loanAmount = parseFloat((newValue * 0.7).toFixed(2))
      }
    }
  })

  // 监听付款方式变化
  watch(() => form.paymentMethod, (newValue, oldValue) => {
    // 如果从分期切换到全款，需要重新计算成交价格（去掉分期服务费的影响）
    if (oldValue === 'LOAN' && newValue === 'FULL' && form.loanFee > 0) {
      // 清空分期服务费
      form.loanFee = 0
      // 重新计算成交价格
      calculateFinalPrice(form)
      calculateProfitRate(form)
    }

    if (newValue === 'LOAN' && form.salesAmount > 0) {
      // 如果切换到贷款方式，且车辆销售价格大于0，设置默认的贷款金额和首付金额
      if (form.loanInitialAmount === 0 && form.loanAmount === 0) {
        // 如果贷款金额和首付金额都为0，设置默认值：首付30%，贷款70%
        form.loanInitialRatio = 30
        form.loanInitialAmount = parseFloat((form.salesAmount * 0.3).toFixed(2))
        form.loanAmount = parseFloat((form.salesAmount * 0.7).toFixed(2))
      }

      // 重置分期返利相关字段
      form.loanRebateAmount = 0
      form.loanRebatePayableDeductible = true

      // 如果有分期服务费，重新计算成交价格（加上分期服务费的影响）
      if (form.loanFee > 0) {
        calculateFinalPrice(form)
        calculateProfitRate(form)
        // 注意：分期服务费不影响贷款金额计算，所以这里不需要重新计算贷款相关金额
      }
    }

    // 当切换到贷款方式时，始终确保loanMonths有值，避免验证错误
    if (newValue === 'LOAN') {
      // 如果没有选择分期期数，默认设置为12期
      if (form.loanMonths === null || form.loanMonths === undefined || form.loanMonths === '') {
        form.loanMonths = 12 // 默认设置为12期
      }
    }
  })

  return {
    form,
    formRef,
    rules,
    finalPriceInWan,
    customerSelectorVisible,
    vehicleSelectorVisible,
    selectedOutboundOrg,
    loanChannelOptions,
    loanMonthsOptions,
    showCustomerSelector,
    showVehicleSelector,
    handleCustomerSelected,
    handleVehicleSelected,
    handleOutboundOrgChange,
    handleSalePriceChange,
    handleDiscountChange,
    handleLoanAmountChange,
    handleLoanInitialAmountChange,
    handleLoanFeeChange,
    handleSave,
    handleCancel,
    resetForm
  }
}
