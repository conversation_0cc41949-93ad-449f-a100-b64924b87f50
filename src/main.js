import { createApp } from 'vue'
import { createPinia } from 'pinia'
import naive from 'naive-ui'
import App from '@/App.vue'
import router from '@/router'
import './assets/styles/global.css'
import './styles/naive-ui-override.scss'
import { initAuthEventHandlers } from '@/utils/authHandler'
import tipPosition from '@/directives/tipPosition'

// 修复非被动事件监听器警告
// 简单而安全的解决方案：只在必要时覆盖默认行为
if (typeof window !== 'undefined') {
    // 监听控制台警告并静默处理
    const originalConsoleWarn = console.warn
    console.warn = function (...args) {
        const message = args.join(' ')
        // 过滤掉特定的被动事件监听器警告
        if (message.includes('Added non-passive event listener') &&
            message.includes('wheel')) {
            return // 静默处理这个特定警告
        }
        return originalConsoleWarn.apply(console, args)
    }
}

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(naive)

// 注册自定义指令
app.directive('tip-position', tipPosition)

// 初始化认证事件处理器
initAuthEventHandlers()

app.mount('#app')
