/**
 * 业务字典数据管理
 * 支持从服务器端自动加载字典数据并缓存到本地
 */

import { doGet } from '@/utils/requests'
import { reactive } from 'vue'

// 缓存配置
const CACHE_CONFIG = {
  // 缓存键前缀
  CACHE_PREFIX: 'dict_cache_',
  // 缓存过期时间（毫秒）- 默认30分钟
  CACHE_EXPIRE_TIME: 30 * 60 * 1000,
  // 缓存版本键
  CACHE_VERSION_KEY: 'dict_cache_version',
  // 当前缓存版本
  CURRENT_VERSION: '1.0.0'
}

// 前端维护的字典组（这些字典组不从服务器加载）
const FRONTEND_DICT_GROUPS = ['province_city', 'city_district']

// 字典数据状态
const dictState = {
  // 是否正在加载
  loading: false,
  // 是否已初始化
  initialized: false,
  // 加载失败的字典组
  failedGroups: new Set(),
  // 加载成功的字典组
  loadedGroups: new Set()
}

// 字典列表（包含前端维护的字典）
export const dictList = [
  {
    dict_code: 'province_city',
    dict_name: '省/直辖市',
    remark: '业务组织架构省份',
    dict_type: 'CUSTOMIZE'
  },
  {
    dict_code: 'city_district',
    dict_name: '市/区',
    remark: '业务组织架构城市区域',
    dict_type: 'CUSTOMIZE'
  }
]

// 字典选项（包含前端维护的字典选项和从服务器加载的选项）
// 使用 reactive 使其响应式，这样 Vue 组件可以自动响应数据变化
export const dictOptions = reactive({
  'province_city': [
    {
      optionValue: 'shandong',
      optionLabel: '山东省',
      sort: 1,
      remark: '山东省'
    },
    {
      optionValue: 'hebei',
      optionLabel: '河北省',
      sort: 2,
      remark: '河北省'
    },
    {
      optionValue: 'henan',
      optionLabel: '河南省',
      sort: 3,
      remark: '河南省'
    },
    {
      optionValue: 'chongqing',
      optionLabel: '重庆市',
      sort: 4,
      remark: '重庆市'
    }
  ],

  'city_district': [
    // 山东省城市
    {
      optionValue: 'jinan',
      optionLabel: '济南市',
      sort: 1,
      remark: '山东省济南市',
      parent: 'shandong'
    },
    {
      optionValue: 'dezhou',
      optionLabel: '德州市',
      sort: 2,
      remark: '山东省德州市',
      parent: 'shandong'
    },
    {
      optionValue: 'zibo',
      optionLabel: '淄博市',
      sort: 3,
      remark: '山东省淄博市',
      parent: 'shandong'
    },
    {
      optionValue: 'linyi',
      optionLabel: '临沂市',
      sort: 4,
      remark: '山东省临沂市',
      parent: 'shandong'
    },
    {
      optionValue: 'liaocheng',
      optionLabel: '聊城市',
      sort: 5,
      remark: '山东省聊城市',
      parent: 'shandong'
    },
    {
      optionValue: 'dongying',
      optionLabel: '东营市',
      sort: 6,
      remark: '山东省东营市',
      parent: 'shandong'
    },
    {
      optionValue: 'binzhou',
      optionLabel: '滨州市',
      sort: 7,
      remark: '山东省滨州市',
      parent: 'shandong'
    },
    // 河南省城市
    {
      optionValue: 'zhengzhou',
      optionLabel: '郑州市',
      sort: 8,
      remark: '河南省郑州市',
      parent: 'henan'
    },
    {
      optionValue: 'luoyang',
      optionLabel: '洛阳市',
      sort: 9,
      remark: '河南省洛阳市',
      parent: 'henan'
    },
    {
      optionValue: 'zhumadian',
      optionLabel: '驻马店市',
      sort: 10,
      remark: '河南省驻马店市',
      parent: 'henan'
    },
    // 河北省城市
    {
      optionValue: 'shijiazhuang',
      optionLabel: '石家庄市',
      sort: 11,
      remark: '河北省石家庄市',
      parent: 'hebei'
    },
    {
      optionValue: 'cangzhou',
      optionLabel: '沧州市',
      sort: 12,
      remark: '河北省沧州市',
      parent: 'hebei'
    },
    {
      optionValue: 'langfang',
      optionLabel: '廊坊市',
      sort: 13,
      remark: '河北省廊坊市',
      parent: 'hebei'
    },
    // 重庆市区县
    {
      optionValue: 'yuzhong',
      optionLabel: '渝中区',
      sort: 14,
      remark: '重庆市渝中区',
      parent: 'chongqing'
    },
    {
      optionValue: 'jiangbei',
      optionLabel: '江北区',
      sort: 15,
      remark: '重庆市江北区',
      parent: 'chongqing'
    }
  ]
})

/**
 * 缓存管理工具
 */
const cacheManager = {
  /**
   * 检查缓存是否有效
   * @param {string} key - 缓存键
   * @returns {boolean} 是否有效
   */
  isValid(key) {
    try {
      const cached = localStorage.getItem(key)
      if (!cached) return false

      const data = JSON.parse(cached)
      const now = Date.now()

      // 检查版本
      const version = localStorage.getItem(CACHE_CONFIG.CACHE_VERSION_KEY)
      if (version !== CACHE_CONFIG.CURRENT_VERSION) {
        this.clearAll()
        return false
      }

      // 检查过期时间
      return data.timestamp && (now - data.timestamp) < CACHE_CONFIG.CACHE_EXPIRE_TIME
    } catch (error) {
      console.warn('检查缓存有效性失败:', error)
      return false
    }
  },

  /**
   * 获取缓存数据
   * @param {string} key - 缓存键
   * @returns {any} 缓存的数据
   */
  get(key) {
    try {
      if (!this.isValid(key)) return null

      const cached = localStorage.getItem(key)
      const data = JSON.parse(cached)
      return data.value
    } catch (error) {
      console.warn('获取缓存数据失败:', error)
      return null
    }
  },

  /**
   * 设置缓存数据
   * @param {string} key - 缓存键
   * @param {any} value - 要缓存的数据
   */
  set(key, value) {
    try {
      const data = {
        value,
        timestamp: Date.now()
      }
      localStorage.setItem(key, JSON.stringify(data))

      // 设置版本标识
      localStorage.setItem(CACHE_CONFIG.CACHE_VERSION_KEY, CACHE_CONFIG.CURRENT_VERSION)
    } catch (error) {
      console.warn('设置缓存数据失败:', error)
    }
  },

  /**
   * 删除指定缓存
   * @param {string} key - 缓存键
   */
  remove(key) {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.warn('删除缓存失败:', error)
    }
  },

  /**
   * 清除所有字典缓存
   */
  clearAll() {
    try {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith(CACHE_CONFIG.CACHE_PREFIX)) {
          localStorage.removeItem(key)
        }
      })
      localStorage.removeItem(CACHE_CONFIG.CACHE_VERSION_KEY)
    } catch (error) {
      console.warn('清除所有缓存失败:', error)
    }
  }
}

/**
 * 字典数据加载器
 */
const dictLoader = {
  /**
   * 从服务器加载字典列表
   * @returns {Promise<Array>} 字典列表
   */
  async loadDictList() {
    try {
      const cacheKey = `${CACHE_CONFIG.CACHE_PREFIX}dict_list`

      // 先尝试从缓存获取
      const cached = cacheManager.get(cacheKey)
      if (cached) {
        console.log('从缓存加载字典列表')
        // 检查缓存数据格式，如果是旧格式则重新加载
        if (cached.length > 0 && !cached[0].dict_code) {
          console.log('缓存数据格式过时，重新加载')
          cacheManager.remove(cacheKey)
        } else {
          return cached
        }
      }

      console.log('从服务器加载字典列表')
      const response = await doGet('/system/options?listAll=true')

      // 添加详细的调试日志
      console.log('字典列表 API响应:', response)

      if (response.code === 200 && Array.isArray(response.data)) {
        console.log('字典列表原始数据:', response.data)

        // 转换数据格式以兼容现有代码，过滤掉无效数据
        const dictList = response.data
          .filter(item => {
            // 过滤掉空值或无效的字典项
            const dictCode = item?.optionGroup || item?.dict_code || item?.code;
            if (!dictCode) {
              console.warn('跳过无效的字典项（缺少字典编码）:', item);
              return false;
            }
            return true;
          })
          .map(item => ({
            dict_code: item.optionGroup || item.dict_code || item.code,
            dict_name: item.optionGroupName || item.dict_name || item.name || item.optionGroup || item.dict_code || item.code,
            remark: item.remark || item.optionGroupName || item.dict_name || item.name || '',
            dict_type: item.optionGroupType || item.dict_type || 'SYSTEM'
          }))

        console.log('字典列表转换后数据:', dictList)

        // 缓存数据
        cacheManager.set(cacheKey, dictList)
        return dictList
      } else {
        throw new Error('字典列表数据格式不正确')
      }
    } catch (error) {
      console.warn('加载字典列表失败:', error)
      return []
    }
  },

  /**
   * 强制从服务器加载指定字典组的选项（跳过缓存）
   * @param {string} dictCode - 字典编码
   * @returns {Promise<Array>} 字典选项列表
   */
  async forceLoadDictOptions(dictCode) {
    try {
      // 前端维护的字典组不从服务器加载
      if (FRONTEND_DICT_GROUPS.includes(dictCode)) {
        return dictOptions[dictCode] || []
      }

      console.log(`强制从服务器加载字典选项: ${dictCode}`)
      const response = await doGet(`/system/options/${dictCode}?listAll=true`)

      // 添加详细的调试日志
      console.log(`字典 ${dictCode} API响应:`, response)

      if (response.code === 200 && Array.isArray(response.data)) {
        console.log(`字典 ${dictCode} 原始数据:`, response.data)
        // 转换数据格式以兼容现有代码
        const options = response.data.map(item => ({
          optionValue: item.optionValue || item.optionValue || item.value,
          optionLabel: item.optionLabel || item.optionLabel || item.label,
          sort: item.optionOrder || item.sort || item.order || 0,
          remark: item.optionComment || item.remark || item.comment || item.optionLabel || item.optionLabel || item.label,
          type: item.type || item.optionType || 'default',
          color: item.color || item.optionColor || '#909399',
          parent: item.parent || item.parentValue
        }))

        console.log(`字典 ${dictCode} 转换后数据:`, options)

        // 强制更新缓存和内存数据
        const cacheKey = `${CACHE_CONFIG.CACHE_PREFIX}options_${dictCode}`
        cacheManager.set(cacheKey, options)
        dictOptions[dictCode] = options
        dictState.loadedGroups.add(dictCode)
        dictState.failedGroups.delete(dictCode)

        return options
      } else {
        throw new Error(`字典选项数据格式不正确: ${dictCode}`)
      }
    } catch (error) {
      console.warn(`强制加载字典选项失败: ${dictCode}`, error)
      dictState.failedGroups.add(dictCode)
      return []
    }
  },

  /**
   * 从服务器加载指定字典组的选项
   * @param {string} dictCode - 字典编码
   * @returns {Promise<Array>} 字典选项列表
   */
  async loadDictOptions(dictCode) {
    try {
      // 前端维护的字典组不从服务器加载
      if (FRONTEND_DICT_GROUPS.includes(dictCode)) {
        return dictOptions[dictCode] || []
      }

      const cacheKey = `${CACHE_CONFIG.CACHE_PREFIX}options_${dictCode}`

      // 先尝试从缓存获取
      const cached = cacheManager.get(cacheKey)
      if (cached) {
        console.log(`从缓存加载字典选项: ${dictCode}`)
        return cached
      }

      console.log(`从服务器加载字典选项: ${dictCode}`)
      const response = await doGet(`/system/options/${dictCode}?listAll=true`)

      // 添加详细的调试日志
      console.log(`字典 ${dictCode} API响应:`, response)

      if (response.code === 200 && Array.isArray(response.data)) {
        console.log(`字典 ${dictCode} 原始数据:`, response.data)
        // 转换数据格式以兼容现有代码
        const options = response.data.map(item => ({
          optionValue: item.optionValue || item.optionValue || item.value,
          optionLabel: item.optionLabel || item.optionLabel || item.label,
          sort: item.optionOrder || item.sort || item.order || 0,
          remark: item.optionComment || item.remark || item.comment || item.optionLabel || item.optionLabel || item.label,
          type: item.type || item.optionType || 'default',
          color: item.color || item.optionColor || '#909399',
          parent: item.parent || item.parentValue
        }))

        console.log(`字典 ${dictCode} 转换后数据:`, options)

        // 缓存数据
        cacheManager.set(cacheKey, options)
        dictState.loadedGroups.add(dictCode)
        dictState.failedGroups.delete(dictCode)

        return options
      } else {
        throw new Error(`字典选项数据格式不正确: ${dictCode}`)
      }
    } catch (error) {
      console.warn(`加载字典选项失败: ${dictCode}`, error)
      dictState.failedGroups.add(dictCode)
      return []
    }
  },

  /**
   * 批量加载所有字典数据
   * @returns {Promise<void>}
   */
  async loadAllDictionaries() {
    if (dictState.loading) {
      console.log('字典数据正在加载中，跳过重复加载')
      return
    }

    dictState.loading = true

    try {
      console.log('开始加载所有字典数据')

      // 1. 加载字典列表
      const serverDictList = await this.loadDictList()

      // 2. 合并前端维护的字典列表
      const allDictList = [...dictList, ...serverDictList]

      // 3. 批量加载所有字典选项
      console.log('准备加载的字典列表:', allDictList)

      const loadPromises = allDictList.map(async (dict) => {
        try {
          // 检查字典编码是否有效
          if (!dict || !dict.dict_code) {
            console.warn('跳过无效的字典项:', dict)
            return
          }

          console.log(`开始加载字典: ${dict.dict_code}`)
          const options = await this.loadDictOptions(dict.dict_code)
          if (options.length > 0) {
            dictOptions[dict.dict_code] = options
            console.log(`字典 ${dict.dict_code} 加载成功，共 ${options.length} 个选项`)
          } else {
            console.warn(`字典 ${dict.dict_code} 加载结果为空`)
          }
        } catch (error) {
          console.warn(`加载字典 ${dict?.dict_code || 'unknown'} 失败:`, error)
        }
      })

      await Promise.allSettled(loadPromises)

      dictState.initialized = true
      console.log('字典数据加载完成')

    } catch (error) {
      console.error('加载字典数据失败:', error)
    } finally {
      dictState.loading = false
    }
  }
}

/**
 * 字典数据管理器 - 对外提供的主要API
 */
export const dictManager = {
  /**
   * 初始化字典数据
   * @param {boolean} force - 是否强制重新加载
   * @returns {Promise<void>}
   */
  async initialize(force = false) {
    if (force) {
      dictState.initialized = false
      cacheManager.clearAll()
    }

    if (!dictState.initialized && !dictState.loading) {
      await dictLoader.loadAllDictionaries()
    }
  },

  /**
   * 获取字典选项（兼容原有调用方式）
   * @param {string} dictCode - 字典编码
   * @returns {Promise<Array>} 字典选项列表
   */
  async getDictOptions(dictCode) {
    // 确保字典数据已初始化
    await this.initialize()

    // 如果该字典组还未加载，尝试单独加载
    if (!dictOptions[dictCode] && !dictState.failedGroups.has(dictCode)) {
      const options = await dictLoader.loadDictOptions(dictCode)
      if (options.length > 0) {
        dictOptions[dictCode] = options
      }
    }

    return dictOptions[dictCode] || []
  },

  /**
   * 刷新指定字典组
   * @param {string} dictCode - 字典编码
   * @returns {Promise<Array>} 字典选项列表
   */
  async refreshDictOptions(dictCode) {
    // 清除缓存
    const cacheKey = `${CACHE_CONFIG.CACHE_PREFIX}options_${dictCode}`
    cacheManager.remove(cacheKey)

    // 重新加载
    const options = await dictLoader.loadDictOptions(dictCode)
    if (options.length > 0) {
      dictOptions[dictCode] = options
    }

    return options
  },

  /**
   * 刷新所有字典数据
   * @returns {Promise<void>}
   */
  async refreshAll() {
    cacheManager.clearAll()
    dictState.initialized = false
    dictState.loadedGroups.clear()
    dictState.failedGroups.clear()

    // 清空现有的服务器端字典数据（保留前端维护的数据）
    Object.keys(dictOptions).forEach(key => {
      if (!FRONTEND_DICT_GROUPS.includes(key)) {
        delete dictOptions[key]
      }
    })

    await this.initialize()
  },

  /**
   * 获取字典数据状态
   * @returns {Object} 状态信息
   */
  getStatus() {
    return {
      loading: dictState.loading,
      initialized: dictState.initialized,
      loadedGroups: Array.from(dictState.loadedGroups),
      failedGroups: Array.from(dictState.failedGroups),
      totalGroups: Object.keys(dictOptions).length
    }
  },

  /**
   * 清除所有缓存
   */
  clearCache() {
    cacheManager.clearAll()
  }
}

// 初始化状态管理
let initPromise = null

/**
 * 获取初始化Promise（用于等待初始化完成）
 * @returns {Promise<void>}
 */
export function getInitPromise() {
  return initPromise
}

/**
 * 设置初始化Promise（由外部调用者设置）
 * @param {Promise<void>} promise - 初始化Promise
 */
export function setInitPromise(promise) {
  initPromise = promise
}

// 导出dictLoader供外部使用
export { dictLoader }
