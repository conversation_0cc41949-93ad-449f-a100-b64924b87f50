/**
 * 字典数据加载测试工具
 * 用于测试字典数据的自动加载和缓存功能
 */

import { dictManager, dictOptions } from '@/mock/dictData'
import { getDictOptions, getDictOptionsAsync, dictManagementUtils } from '@/utils/dictUtils'

/**
 * 测试字典数据加载功能
 */
export async function testDictLoading() {
  console.log('=== 开始测试字典数据加载功能 ===')
  
  try {
    // 1. 测试初始化
    console.log('1. 测试字典数据初始化...')
    await dictManagementUtils.initialize()
    
    const status = dictManagementUtils.getStatus()
    console.log('初始化状态:', status)
    
    // 2. 测试同步获取字典选项
    console.log('2. 测试同步获取字典选项...')
    const syncOptions = getDictOptions('vehicle_brand')
    console.log('同步获取车辆品牌选项:', syncOptions.length, '个')
    
    // 3. 测试异步获取字典选项
    console.log('3. 测试异步获取字典选项...')
    const asyncOptions = await getDictOptionsAsync('order_status')
    console.log('异步获取订单状态选项:', asyncOptions.length, '个')
    
    // 4. 测试缓存功能
    console.log('4. 测试缓存功能...')
    const startTime = Date.now()
    await getDictOptionsAsync('stock_status')
    const firstLoadTime = Date.now() - startTime
    
    const startTime2 = Date.now()
    await getDictOptionsAsync('stock_status')
    const secondLoadTime = Date.now() - startTime2
    
    console.log(`首次加载耗时: ${firstLoadTime}ms, 缓存加载耗时: ${secondLoadTime}ms`)
    
    // 5. 测试刷新功能
    console.log('5. 测试刷新功能...')
    await dictManagementUtils.refresh('vehicle_brand')
    console.log('刷新车辆品牌字典完成')
    
    // 6. 显示所有已加载的字典
    console.log('6. 已加载的字典组:')
    Object.keys(dictOptions).forEach(dictCode => {
      const options = dictOptions[dictCode]
      console.log(`  - ${dictCode}: ${options.length} 个选项`)
    })
    
    console.log('=== 字典数据加载测试完成 ===')
    return true
    
  } catch (error) {
    console.error('字典数据加载测试失败:', error)
    return false
  }
}

/**
 * 测试字典工具函数
 */
export async function testDictUtils() {
  console.log('=== 开始测试字典工具函数 ===')
  
  try {
    // 确保字典数据已加载
    await dictManagementUtils.waitForInitialization()
    
    // 测试各种工具函数
    console.log('测试车辆品牌工具函数...')
    const brandOptions = getDictOptions('vehicle_brand')
    if (brandOptions.length > 1) {
      const firstBrand = brandOptions[1] // 跳过"不限"选项
      console.log(`品牌标签: ${firstBrand.label}`)
      console.log(`品牌值: ${firstBrand.value}`)
    }
    
    console.log('测试订单状态工具函数...')
    const statusOptions = getDictOptions('order_status')
    if (statusOptions.length > 1) {
      const firstStatus = statusOptions[1]
      console.log(`状态标签: ${firstStatus.label}`)
      console.log(`状态值: ${firstStatus.value}`)
    }
    
    console.log('=== 字典工具函数测试完成 ===')
    return true
    
  } catch (error) {
    console.error('字典工具函数测试失败:', error)
    return false
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('开始运行字典数据测试套件...')
  
  const results = {
    loading: await testDictLoading(),
    utils: await testDictUtils()
  }
  
  console.log('测试结果:', results)
  
  const allPassed = Object.values(results).every(result => result === true)
  console.log(allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败')
  
  return results
}

// 在开发环境下自动运行测试
if (import.meta.env.DEV) {
  // 延迟执行，确保模块加载完成
  setTimeout(() => {
    runAllTests().catch(console.error)
  }, 1000)
}
