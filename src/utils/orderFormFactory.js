/**
 * 订单表单数据工厂函数
 */
import { generateOrderNo, convertDateToTimestamp } from './orderFormUtils'
import { convertNumberToChinese, convertCentsToYuan } from './money'

/**
 * 批量转换金额字段从分到元
 * @param {Object} data - 源数据
 * @param {Object} formData - 目标表单数据
 * @param {Array} fieldMappings - 字段映射配置数组
 */
const convertAmountFields = (data, formData, fieldMappings) => {
  fieldMappings.forEach(({ source, target }) => {
    if (data[source] !== undefined) {
      formData[target || source] = convertCentsToYuan(data[source])
    }
  })
}

/**
 * 批量复制同名字段
 * @param {Object} source - 源数据对象
 * @param {Object} target - 目标数据对象
 * @param {Array} fields - 字段名数组
 * @param {Function} transform - 可选的转换函数
 */
const copyFields = (source, target, fields, transform = null) => {
  fields.forEach(field => {
    if (source[field] !== undefined) {
      target[field] = transform ? transform(source[field]) : source[field]
    }
  })
}

/**
 * 创建默认的订单表单数据
 * @returns {Object} 默认表单数据
 */
export const createDefaultOrderForm = () => {
  return {
    id: null,
    batchCode: generateOrderNo(), // 自动生成订单编号
    dealDate: Date.now(), // 默认为当前日期
    salesOrgId: null, // 销售单位ID
    salesOrgName: '',
    remark: '',
    orderStatus: 'PENDING', // 默认为大定金状态

    // 出库信息
    deliveryOrgId: null, // 出库单位ID
    outboundOrgName: '', // 出库单位名称
    deliveryDate: Date.now(), // 默认为当前日期

    // 客户信息
    customerId: null,
    customerName: '',
    customerType: 'individual', // 客户类型，默认为个人客户
    customerPhone: '',
    customerAddress: '',
    salesAgentName: '',
    salesAgentId: null, // 销售顾问ID
    salesLeaderId: null, // 销售主管ID

    // 车辆信息
    skuId: null, // 车辆SKU ID
    vehicleBrand: '', // 车辆品牌
    vehicleSeries: '', // 车型
    vehicleConfig: '', // 配置
    vehicleColorCode: '', // 颜色代码
    sbAmount: 0, // 启票价格
    salesAmount: 0, // 销售价格

    // 金额信息
    depositAmount: 0, // 已付定金
    depositDeductible: true, // 定金是否转车款，默认为是
    discountAmount: 0, // 优惠金额
    discountDeductible: true, // 优惠金额是否转车款，默认为是
    hasExclusiveDiscount: 'NO', // 是否享受专项优惠补贴，默认为"否"，可选值：YES（是）、NO（否）
    exclusiveDiscountType: null, // 专享优惠类型
    exclusiveDiscountAmount: 0, // 专享优惠金额（保留兼容性）
    exclusiveDiscountPayableDeductible: false, // 专享优惠金额是否转车款（保留兼容性）
    exclusiveDiscountReceivableAmount: 0, // 应收-厂家-专项优惠金额
    exclusiveDiscountPayableAmount: 0, // 应付-客户-专项优惠金额
    exclusiveDiscountPayableDeductible: true, // 应付客户专项优惠是否转车款，默认为是
    exclusiveDiscountRemark: '', // 专项优惠备注
    dealAmount: 0, // 成交总价
    dealAmountCn: '零', // 成交总价（大写）
    profitRate: 0, // 预估毛利率
    grossProfitAmount: 0, // 预估毛利润

    // 付款方式
    paymentMethod: 'FULL', // 默认全款，可选值：FULL（全款）、LOAN（贷款）

    // 贷款信息 - 付款方式为贷款时使用
    loanChannel: '', // 贷款渠道
    loanAmount: 0, // 贷款金额
    loanInitialAmount: 0, // 首付金额
    loanInitialRatio: 0, // 首付比例
    loanMonths: null, // 贷款期限（月）
    loanRebateAmount: 0, // 分期返利（客户）
    loanRebatePayableDeductible: true, // 分期返利是否转车款，默认为是
    loanFee: 0, // 分期服务费，默认为0
    loanRebateReceivableAmount: 0, // 应收-机构-分期返利
    loanRebatePayableAmount: 0, // 应付-客户-分期返利
    loanRebatePayableDeductible: true, // 应付客户分期返利是否转车款，默认为是

    // 二手车置换信息
    hasUsedVehicle: 'NO', // 是否有车辆置换，默认为"无"，可选值：YES（有）、NO（无）
    usedVehicleId: '', // 二手车车牌号
    usedVehicleVin: '', // 二手车VIN
    usedVehicleAmount: 0, // 二手车置换金额
    usedVehicleDiscountPayableDeductible: true, // 是否转车款，默认为是
    usedVehicleDiscountAmount: 0, // 二手车置换补贴
    usedVehicleDiscountReceivableAmount: 0, // 应收-厂家-置换补贴
    usedVehicleDiscountPayableAmount: 0, // 应付-客户-置换补贴
    usedVehicleDiscountPayableDeductible: true, // 置换补贴是否转车款，默认为是（保留兼容性）
    usedVehicleDiscountPayableDeductible: true, // 应付客户置换补贴是否转车款，默认为是
    usedVehicleBound: '', // 二手车品牌
    usedVehicleModel: '', // 二手车车型
    usedVehicleColor: '', // 二手车颜色

    // 车辆保险信息
    hasInsurance: 'NO', // 是否购买保险，默认为"无"，可选值：YES（有）、NO（无）
    insuranceOrgId: null, // 保险经办机构ID
    insuranceOrgName: '', // 保险经办机构名称

    // 其他衍生收入信息
    hasDerivativeIncome: 'NO', // 是否产生其他衍生收入，默认为"无"，可选值：YES（有）、NO（无）
    notaryFee: 0, // 公证费
    carefreeIncome: 0, // 畅行无忧收入
    extendedWarrantyIncome: 0, // 延保收入
    vpsIncome: 0, // VPS收入
    preInterest: 0, // 前置利息
    licensePlateFee: 0, // 挂牌费
    tempPlateFee: 0, // 临牌费
    deliveryEquipment: 0, // 外卖装具

    // 赠品明细信息
    hasGiftItems: 'NO', // 是否有赠品，默认为"无"，可选值：YES（有）、NO（无）
    giftItems: [] // 赠品明细数组
  }
}

/**
 * 合并初始数据到表单
 * @param {Object} form - 表单数据对象
 * @param {Object} initialData - 初始数据
 * @returns {Object} 合并后的表单数据
 */
export const mergeInitialData = (form, initialData) => {
  if (!initialData) return form

  const data = { ...initialData }

  // 确保日期是时间戳格式
  if (data.dealDate) {
    data.dealDate = convertDateToTimestamp(data.dealDate)
  }

  // 合并数据
  Object.assign(form, data)

  return form
}

/**
 * 将API数据转换为表单数据
 * @param {Object} apiData - API返回的数据
 * @param {Object} form - 表单数据对象
 * @returns {Object} 转换后的表单数据
 */
export const convertApiDataToForm = (apiData, form) => {
  if (!apiData) return form

  const formData = { ...form }
  const data = { ...apiData }

  // 定义金额字段映射配置
  const basicAmountFields = [
    { source: 'sbAmount', target: 'sbAmount' },
    { source: 'salesAmount', target: 'salesAmount' },
    { source: 'invoiceAmount', target: 'invoicePrice' }, // 字段名映射
    { source: 'depositAmount', target: 'depositAmount' },
    { source: 'discountAmount', target: 'discountAmount' }
  ]

  // 批量转换基础金额字段
  convertAmountFields(data, formData, basicAmountFields)

  // 处理默认值
  if (data.depositDeductible === undefined) formData.depositDeductible = true

  // 处理专享优惠信息
  if (data.hasExclusiveDiscount !== undefined) {
    formData.hasExclusiveDiscount = data.hasExclusiveDiscount
  } else {
    // 如果有专享优惠类型或任何专项优惠金额，则设置为"是"
    formData.hasExclusiveDiscount = (data.exclusiveDiscountType ||
      data.exclusiveDiscountAmount ||
      data.exclusiveDiscountReceivableAmount ||
      data.exclusiveDiscountPayableAmount) ? 'YES' : 'NO'
  }

  if (data.exclusiveDiscountType !== undefined) formData.exclusiveDiscountType = data.exclusiveDiscountType
  // 定义专项优惠和成交相关金额字段
  const advancedAmountFields = [
    { source: 'exclusiveDiscountAmount', target: 'exclusiveDiscountAmount' },
    { source: 'exclusiveDiscountReceivableAmount', target: 'exclusiveDiscountReceivableAmount' },
    { source: 'exclusiveDiscountPayableAmount', target: 'exclusiveDiscountPayableAmount' },
    { source: 'dealAmount', target: 'dealAmount' },
  ]

  // 批量转换高级金额字段
  convertAmountFields(data, formData, advancedAmountFields)

  // 处理专项优惠非金额字段
  copyFields(data, formData, [
    'exclusiveDiscountPayableDeductible',
    'exclusiveDiscountPayableDeductible',
    'exclusiveDiscountRemark'
  ])

  // 处理成交总价大写
  if (data.dealAmountCn !== undefined) {
    formData.dealAmountCn = data.dealAmountCn
  } else {
    formData.dealAmountCn = convertNumberToChinese(formData.dealAmount || 0)
  }

  // 处理付款方式
  if (data.paymentMethod !== undefined) formData.paymentMethod = data.paymentMethod

  // 处理贷款信息
  // 定义贷款相关金额字段
  const loanAmountFields = [
    { source: 'loanAmount', target: 'loanAmount' },
    { source: 'loanInitialAmount', target: 'loanInitialAmount' },
    { source: 'loanFee', target: 'loanFee' },
    { source: 'loanRebateReceivableAmount', target: 'loanRebateReceivableAmount' },
    { source: 'loanRebatePayableAmount', target: 'loanRebatePayableAmount' }
  ]

  // 批量转换贷款金额字段
  convertAmountFields(data, formData, loanAmountFields)

  // 处理贷款非金额字段
  copyFields(data, formData, ['loanChannel', 'loanInitialRatio', 'loanRebatePayableDeductible'])

  // 特殊处理 loanMonths 字段（需要类型转换）
  if (data.loanMonths !== undefined) {
    formData.loanMonths = typeof data.loanMonths === 'number' ? data.loanMonths : parseInt(data.loanMonths) || null
  }

  // 处理二手车信息
  if (data.usedVehicleId !== undefined) {
    formData.usedVehicleId = data.usedVehicleId
    // 如果有二手车ID，则设置hasUsedVehicle为"有"
    formData.hasUsedVehicle = 'YES'
  } else {
    // 如果没有二手车ID，则设置hasUsedVehicle为"无"
    formData.hasUsedVehicle = 'NO'
  }

  // 处理二手车其他字段
  if (data.usedVehicleVin !== undefined) formData.usedVehicleVin = data.usedVehicleVin

  // 处理二手车金额字段（需要从分转换为元）
  copyFields(data, formData, ['usedVehicleAmount', 'usedVehicleDiscountAmount'],
    value => parseFloat((value / 100).toFixed(2)))

  // 处理其他衍生收入信息
  if (data.hasDerivativeIncome !== undefined) formData.hasDerivativeIncome = data.hasDerivativeIncome

  // 处理衍生收入金额字段（从分转换为元）
  copyFields(data, formData, [
    'notaryFee', 'carefreeIncome', 'extendedWarrantyIncome', 'vpsIncome',
    'preInterest', 'licensePlateFee', 'tempPlateFee', 'deliveryEquipment', 'otherIncome'
  ], value => parseFloat((value / 100).toFixed(2)))

  // 处理保险信息
  copyFields(data, formData, ['hasInsurance', 'insuranceOrgId', 'insuranceOrgName'])

  // 处理赠品明细信息
  if (data.hasGiftItems !== undefined) formData.hasGiftItems = data.hasGiftItems
  formData.giftItems = (data.giftItems !== undefined && Array.isArray(data.giftItems)) ? data.giftItems : []

  return formData
}

export default {
  createDefaultOrderForm,
  mergeInitialData,
  convertApiDataToForm
}
