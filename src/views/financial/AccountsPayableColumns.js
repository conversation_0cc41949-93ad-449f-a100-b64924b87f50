/**
 * 应付账款页面表格列配置
 */
import { h } from "vue";
import { NButton, NSpace, NIcon, NTag } from "naive-ui";
import { CheckmarkCircleOutline, CloseCircleOutline } from "@vicons/ionicons5";
import { renderReceivableAmount } from "@/utils/money";
import { renderTextWithLinks } from "@/utils/textLinkRenderer";

/**
 * 创建应付账款表格列配置
 * @param {Object} handlers - 事件处理函数对象
 * @param {Function} handlers.onOrderClick - 订单号点击处理
 * @param {Function} handlers.onVinClick - 车架号点击处理
 * @param {Function} handlers.confirmPayment - 确认付款处理
 * @param {Function} handlers.returnPayable - 退回应付账款处理
 * @returns {Array} 表格列配置数组
 */
export const createAccountsPayableColumns = (handlers = {}) => {
  const {
    onOrderClick,
    onVinClick,
    confirmPayment,
    returnPayable
  } = handlers;

  return [
    {
      type: "selection",
      width: 50,
      align: "center",
      disabled: (row) => {
        // 已确认或已退回的记录不能被选择
        return row.billStatus === 'CONFIRMED' || row.billStatus === 'RETURNED';
      }
    },
    {
      title: "应付标识",
      width: 100,
      key: "id",
      align: "center",
    },
    {
      title: "应付日期",
      key: "createTime",
      width: 120,
      align: "center",
      render(row) {
        if (!row.createTime) return "-";
        // 只显示日期部分
        return row.createTime.split(" ")[0];
      },
    },
    {
      title: "付款方",
      key: "payableOrgName",
      width: 150,
      align: "center",
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return row.payableOrgName || "-";
      },
    },


    {
      title: "应付科目",
      key: "feeName",
      width: 140,
      align: "center",
      ellipsis: {
        tooltip: true,
      },
    },

    {
      title: "收款方",
      key: "feeTarget",
      width: 120,
      align: "center",
      ellipsis: {
        tooltip: true,
      },
    },

    {
      title: "付款摘要",
      key: "payableSummary",
      width: 260,
      align: "left",
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        const summary = row.payableSummary || "";
        return renderTextWithLinks(summary, {
          onOrderClick,
          onVinClick
        });
      },
    },
    {
      title: "应付金额",
      key: "feeAmount",
      width: 150,
      align: "center",
      render(row) {
        return renderReceivableAmount(row.feeAmount);
      },
    },
    {
      title: "应付状态",
      key: "billStatus",
      width: 120,
      align: "center",
      render(row) {
        // 使用 billStatus 字段，参考应付状态字典值
        const billStatus = row.billStatus;

        // 根据字典值渲染不同状态
        switch (billStatus) {
          case 'RETURNED':
            return h(
              NTag,
              {
                type: "warning",
                bordered: false,
                style: {
                  padding: "2px 8px",
                  fontWeight: "bold",
                },
              },
              { default: () => "已退回" }
            );
          case 'CONFIRMED':
            return h(
              NTag,
              {
                type: "success",
                bordered: false,
                style: {
                  padding: "2px 8px",
                  fontWeight: "bold",
                },
              },
              { default: () => "已付款" }
            );
          case 'NOT_CONFIRM':
          default:
            return h(
              NTag,
              {
                type: "error",
                bordered: false,
                style: {
                  padding: "2px 8px",
                  fontWeight: "bold",
                },
              },
              { default: () => "未付款" }
            );
        }
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 140,
      align: "center",
      render(row) {
        const billStatus = row.billStatus;

        // 如果已付款或已退回，不显示任何按钮
        if (billStatus === 'CONFIRMED' || billStatus === 'RETURNED') {
          return null;
        }

        // 如果未付款，显示确认按钮和退回按钮
        return h(NSpace, { size: "small", justify: "center" }, () => [
          // 确认按钮
          h(
            NButton,
            {
              size: "small",
              type: "success",
              quaternary: true,
              onClick: () => confirmPayment && confirmPayment(row),
              style: "color: #18a058; padding: 4px 8px; border-radius: 4px;",
            },
            {
              default: () =>
                h(
                  NIcon,
                  { size: 24 },
                  { default: () => h(CheckmarkCircleOutline) }
                ),
            }
          ),
          // 退回按钮
          h(
            NButton,
            {
              size: "small",
              type: "error",
              quaternary: true,
              onClick: () => returnPayable && returnPayable(row),
              style: "color: #d03050; padding: 4px 8px; border-radius: 4px;",
            },
            {
              default: () =>
                h(
                  NIcon,
                  { size: 24 },
                  { default: () => h(CloseCircleOutline) }
                ),
            }
          ),
        ]);
      },
    },
  ];
};

export default {
  createAccountsPayableColumns
};
