/* 衍生费用管理页面样式 */

.derivative-costs-page {
  padding: 16px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.filter-card {
  margin-bottom: 0;
  flex-shrink: 0;
}

.toolbar {
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: flex-start;
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: 8px;
}

.custom-date-picker {
  width: 280px;
}

/* 机构选择器样式 */
.filter-options .n-tag {
  max-width: 150px;
}

.filter-options .n-tag .n-tag__content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 已选择机构按钮样式 */
.selected-org-button {
  margin-right: 8px;
  margin-bottom: 4px;
  max-width: none;
  /* 允许按钮根据内容自适应宽度 */
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.selected-org-button .org-name {
  flex: 1;
}

.selected-org-button .close-icon {
  opacity: 0.7;
  transition: opacity 0.2s ease;
  cursor: pointer;
}

.selected-org-button .close-icon:hover {
  opacity: 1;
}

/* 清除全部按钮特殊样式 */
.clear-all-button {
  border-color: #d03050 !important;
  color: #d03050 !important;
}

.clear-all-button:hover {
  background-color: rgba(208, 48, 80, 0.1) !important;
  border-color: #d03050 !important;
  color: #d03050 !important;
}

/* 自定义单选按钮组样式已移至全局样式文件 src/assets/styles/global.css */

/* 表格容器样式 - 优化虚拟滚动 */
.table-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  background-color: #fff;
  min-height: 0;
  /* 确保flex子项可以收缩 */
  overflow: hidden;
  width: 100%;
}

.data-table-wrapper {
  flex: 1;
  position: relative;
  width: 100%;
  min-height: 0;
  overflow: hidden;
  padding-bottom: 20px;
  /* 确保底部有足够空间 */
}

/* 分页组件样式 - 固定在表格底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 2px 10px;
  background-color: #fafafa;
  min-height: 40px;
  flex-shrink: 0;
  overflow-x: auto;
}

/* 分页组件内部样式优化 */
:deep(.pagination-container .n-pagination) {
  flex-wrap: nowrap;
  white-space: nowrap;
}

:deep(.pagination-container .n-pagination .n-pagination-item) {
  flex-shrink: 0;
}

:deep(.pagination-container .n-pagination .n-pagination-quick-jumper) {
  flex-shrink: 0;
  margin-left: 8px;
}

:deep(.pagination-container .n-pagination .n-pagination-size-picker) {
  flex-shrink: 0;
  margin-left: 8px;
}

/* 表格样式优化 */
:deep(.data-table-wrapper) {

  /* 确保表格填满容器 */
  .n-data-table {
    height: 100%;
    width: 100%;
  }

  /* 表格头部样式 */
  .n-data-table-thead {
    background-color: #fafafa;
  }

  /* 表格头部单元格样式 */
  .n-data-table-th {
    background-color: #fafafa !important;
    font-weight: 600;
    text-align: center !important;
    border-bottom: 2px solid #e0e0e6;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 数据单元格样式 */
  .n-data-table-td {
    text-align: center !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 固定列右侧边框 */
  .n-data-table-th--fixed-right,
  .n-data-table-td--fixed-right {
    border-left: 1px solid var(--n-border-color) !important;
  }

  /* 表格主体滚动优化 */
  .n-data-table-base-table-body {
    /* 确保最后一行完整显示 */
    padding-bottom: 30px !important;

    /* 确保滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  /* 确保表格容器有足够的底部空间 */
  .n-data-table {
    padding-bottom: 20px;
  }
}

/* 编辑状态下的输入框样式 */
:deep(.n-input-number) {
  .n-input-number-input {
    text-align: center;
  }
}

/* 可编辑成本文本样式 */
:deep(.n-data-table-td .editable-cost-text) {
  cursor: pointer !important;
  color: #18a058 !important;
  border-bottom: 1px dashed #18a058 !important;
  padding-bottom: 1px !important;
  transition: all 0.2s ease !important;
  display: inline-block !important;

  &:hover {
    color: #36ad6a !important;
    border-bottom-color: #36ad6a !important;
    transform: translateY(-1px) !important;
  }

  &:active {
    transform: translateY(0) !important;
  }
}

/* 复制图标样式 */
:deep(.n-data-table-td .n-icon) {
  &.copy-icon {
    color: #18a058 !important;
    transition: all 0.2s ease !important;

    &:hover {
      color: #36ad6a !important;
      transform: scale(1.1) !important;
    }

    &:active {
      transform: scale(0.95) !important;
    }
  }
}

/* 折叠表格样式 */
:deep(.n-data-table) {

  /* 父行样式 */
  .n-data-table-tr:not([data-row-key*="child-"]) {
    background-color: #fafafa;
    font-weight: 500;

    &:hover {
      background-color: #f0f0f0;
    }
  }

  /* 子行样式 */
  .n-data-table-tr[data-row-key*="child-"] {
    background-color: #f9fbff;

    &:hover {
      background-color: #f0f7ff;
    }

    .n-data-table-td {
      padding-left: 24px;
      font-size: 13px;
    }
  }

  /* 展开图标样式 */
  .n-data-table-expand-trigger {
    color: #18a058;

    &:hover {
      color: #36ad6a;
    }
  }
}



/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-options {
    flex-wrap: wrap;
  }

  .filter-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .filter-label {
    width: auto;
    line-height: 24px;
  }
}