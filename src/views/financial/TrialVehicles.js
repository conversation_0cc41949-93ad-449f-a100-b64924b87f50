import { ref, reactive, computed, h, onMounted, onUnmounted } from 'vue'
import { NIcon, NTag, NButton, NSpace } from 'naive-ui'
import {
  SearchOutline,
  RefreshOutline,
  AddOutline,
  CreateOutline,
  TrashOutline,
  CopyOutline,
  LogOutOutline,
  CloseOutline
} from '@vicons/ionicons5'
import SearchIcon from '@/components/icons/SearchIcon.vue'
import { Building } from '@vicons/tabler'
import stocksApi from '@/api/stocks'
import { trialVehiclesApi } from '@/api/trialVehicles'
import { getBizOrgList } from '@/api/bizOrg'
import { getDictOptions } from '@/api/dict'
import { vehicleBrandUtils } from '@/utils/dictUtils'

import messages from '@/utils/messages'

export function useTrialVehicles() {
  // 图标组件
  const SearchOutlineIcon = SearchOutline
  const RefreshOutlineIcon = RefreshOutline
  const AddOutlineIcon = AddOutline
  const CreateOutlineIcon = CreateOutline
  const SearchIconComponent = SearchIcon
  const TrashOutlineIcon = TrashOutline
  const LogOutOutlineIcon = LogOutOutline

  // 响应式数据
  const tableRef = ref(null)
  const loading = ref(false)
  const windowHeight = ref(window.innerHeight)
  const windowWidth = ref(window.innerWidth)



  // 业务机构选择器相关状态
  const showOrgSelector = ref(false)
  const selectedOrgs = ref([])

  // 筛选表单
  const filterForm = reactive({
    stockOrgId: [], // 所属单位ID数组
    keywords: '',
    stockStatus: 'unlimited' // 库存状态：unlimited(不限)、stocking(在库)、sold(已售)
  })

  // 数据列表
  const stocksData = ref([])

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    itemCount: 0,
    showQuickJumper: false
  })

  // 选中的行
  const selectedRowKeys = ref([])

  // 新增弹窗相关
  const showAddModal = ref(false)

  // 出库弹窗相关
  const showOutboundModal = ref(false)
  const currentOutboundRow = ref(null)

  // 机构选项
  const orgOptions = ref([])

  // 库存状态选项
  const stockStatusOptions = ref([
    { label: '不限', value: 'unlimited' },
    { label: '在库', value: 'stocking' },
    { label: '已售', value: 'sold' }
  ])



  // 计算表格最大高度
  const tableMaxHeight = computed(() => {
    const baseHeight = 200 // 基础高度（筛选区域 + 工具栏 + 分页等）
    const availableHeight = windowHeight.value - baseHeight
    return Math.max(availableHeight, 400)
  })

  // 过滤后的数据
  const filteredData = computed(() => {
    return stocksData.value
  })

  // 计算已选择的机构文本
  const selectedOrgText = computed(() => {
    if (selectedOrgs.value.length === 0) {
      return '选择所属单位'
    }
    if (selectedOrgs.value.length === 1) {
      return selectedOrgs.value[0].name || selectedOrgs.value[0].orgName
    }
    return `已选择 ${selectedOrgs.value.length} 个单位`
  })

  // 复制文本到剪贴板
  const copyToClipboard = (text) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        messages.success('已复制到剪贴板')
      })
      .catch((err) => {
        console.error('复制失败:', err)
        messages.error('复制失败')
      })
  }

  // 表格列配置
  const columns = [
    {
      type: 'selection',
      width: 50,
      align: 'center'
    },
    {
      title: 'VIN码',
      key: 'vin',
      width: 180,
      align: 'center',
      ellipsis: {
        tooltip: true
      },
      render(row) {
        return h(
          'div',
          {
            style: {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px'
            }
          },
          [
            h('span', { style: { fontWeight: 'bold', color: '#18a058' } }, row.vin),
            h(
              NButton,
              {
                quaternary: true,
                circle: true,
                size: 'tiny',
                onClick: () => copyToClipboard(row.vin)
              },
              {
                icon: () => h(NIcon, { size: 14 }, { default: () => h(CopyOutline) })
              }
            )
          ]
        )
      }
    },
    {
      title: '车辆品牌',
      key: 'brand',
      width: 120,
      align: 'center'
    },
    {
      title: '车型系列',
      key: 'series',
      width: 120,
      align: 'center'
    },
    {
      title: '配置名称',
      key: 'configName',
      width: 200,
      align: 'center',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '车辆颜色',
      key: 'colorCode',
      width: 150,
      align: 'center',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '库存成本(万元)',
      key: 'stockAmount',
      width: 140,
      align: 'center',
      render(row) {
        if (!row.stockAmount) return '-'
        // 数据是分为单位，需要除以1000000转换为万元
        return `${(row.stockAmount / 1000000).toFixed(2)}`
      }
    },
    {
      title: '所属单位',
      key: 'ownerOrgName',
      width: 150,
      align: 'center',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '库龄（天）',
      key: 'stockDays',
      width: 100,
      align: 'center',
      render(row) {
        return row.stockDays >= 0 ? `${row.stockDays}` : '-'
      }
    },
    {
      title: '试驾开始日期',
      key: 'trialingBeginDate',
      width: 140,
      align: 'center',
      render(row) {
        return row.trialingBeginDate ? new Date(row.trialingBeginDate).toLocaleDateString() : '-'
      }
    },
    {
      title: '库存状态',
      key: 'stockStatus',
      width: 120,
      align: 'center',
      render(row) {
        const statusMap = {
          'stocking': { type: 'success', text: '在库' },
          'sold': { type: 'info', text: '已售' }
        }
        const status = statusMap[row.stockStatus] || { type: 'default', text: '未知' }
        return h(NTag, {
          type: status.type,
          size: 'small'
        }, {
          default: () => status.text
        })
      }
    },
    {
      title: '折旧金额(元)',
      key: 'depreciationAmount',
      width: 140,
      align: 'center',
      render(row) {
        if (!row.depreciationAmount && row.depreciationAmount !== 0) return '-'
        // 数据是分为单位，需要除以100转换为元
        return `${(row.depreciationAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '厂家支持(元)',
      key: 'manufacturerSupportAmount',
      width: 140,
      align: 'center',
      render(row) {
        if (!row.manufacturerSupportAmount && row.manufacturerSupportAmount !== 0) return '-'
        // 数据是分为单位，需要除以100转换为元
        return `${(row.manufacturerSupportAmount / 100).toFixed(2)}`
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      align: 'center',
      render(row) {
        const actions = [
          h(NButton, {
            size: 'small',
            type: 'success',
            text: true,
            class: 'action-icon-button',
            onClick: () => viewDetail(row),
            title: '查看详情'
          }, {
            icon: () => h(SearchIconComponent, { size: 18, color: '#18a058' })
          })
        ]

        // 只有库存状态为 'stocking' 时才显示出库按钮
        if (row.stockStatus === 'stocking') {
          actions.push(
            h(NButton, {
              size: 'small',
              type: 'warning',
              text: true,
              class: 'action-icon-button',
              onClick: () => handleOutbound(row),
              title: '出库'
            }, {
              icon: () => h(NIcon, { size: 18 }, { default: () => h(LogOutOutlineIcon) })
            })
          )
        }

        return h(NSpace, { size: 'small', justify: 'center' }, {
          default: () => actions
        })
      }
    }
  ]

  // 刷新数据
  const refreshData = async () => {
    loading.value = true
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: pagination.pageSize,
        trial: 'trialing' // 固定为试驾状态
      }

      // 添加库存状态筛选（不限时不传stockStatus参数）
      if (filterForm.stockStatus !== 'unlimited') {
        params.stockStatus = filterForm.stockStatus
      }

      // 添加关键词筛选
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords
      }

      // 添加所属单位筛选
      if (filterForm.stockOrgId.length > 0) {
        params.stockOrgId = filterForm.stockOrgId.join(',')
      }

      // 调用库存明细 API
      const response = await stocksApi.getStocksList(params)

      if (response.code === 200) {
        // 直接使用返回的数据列表
        stocksData.value = response.data.list

        // 更新分页信息
        pagination.itemCount = response.data.total
        pagination.pageCount = response.data.pages

        // 确保当前页码与API返回的一致
        if (pagination.page !== response.data.pageNum) {
          pagination.page = response.data.pageNum
        }

        // 如果当前页大于总页数且总页数不为0，则跳转到最后一页
        if (pagination.page > response.data.pages && response.data.pages > 0) {
          pagination.page = response.data.pages
          refreshData()
          return
        }
      } else {
        messages.error(response.message || '数据加载失败')
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      messages.error('加载数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }



  // 搜索处理
  const handleSearch = () => {
    pagination.page = 1
    refreshData()
  }

  // 关键词清除处理
  const handleKeywordsClear = () => {
    filterForm.keywords = ''
    handleSearch()
  }

  // 分页变化处理
  const handlePageChange = (page) => {
    pagination.page = page
    refreshData()
  }

  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    refreshData()
  }

  // 行选择处理
  const handleSelectionChange = (keys) => {
    selectedRowKeys.value = keys
  }

  // 获取行键
  const getRowKey = (row) => {
    return row.id
  }

  // 机构选择处理
  const handleOrgSelect = (orgs) => {
    selectedOrgs.value = orgs
    filterForm.stockOrgId = orgs.map(org => org.id)
    showOrgSelector.value = false
    handleSearch()
  }

  const handleOrgCancel = () => {
    showOrgSelector.value = false
  }

  const clearOrgSelection = () => {
    selectedOrgs.value = []
    filterForm.stockOrgId = []
    handleSearch()
  }

  const removeOrg = (orgId) => {
    selectedOrgs.value = selectedOrgs.value.filter(org => org.id !== orgId)
    filterForm.stockOrgId = selectedOrgs.value.map(org => org.id)
    handleSearch()
  }

  // 库存状态变化处理
  const handleStockStatusChange = (status) => {
    filterForm.stockStatus = status
    handleSearch()
  }



  // 批量更新状态
  const batchUpdateStatus = () => {
    if (selectedRowKeys.value.length === 0) {
      messages.warning('请先选择要更新的试驾车')
      return
    }

    // 这里可以弹出状态选择对话框
    messages.info('批量更新状态功能开发中...')
  }

  // 查看详情
  const viewDetail = (row) => {
    messages.info(`查看试驾车详情: ${row.vin}`)
    // 这里可以打开详情弹窗或跳转到详情页面
  }

  // 编辑试驾车
  const editVehicle = (row) => {
    messages.info(`编辑试驾车: ${row.vin}`)
    // 这里可以打开编辑弹窗
  }

  // 删除试驾车
  const deleteVehicle = (row) => {
    messages.warning(`删除试驾车: ${row.vin}`)
    // 这里可以弹出确认对话框
  }

  // 出库处理
  const handleOutbound = (row) => {
    currentOutboundRow.value = row
    showOutboundModal.value = true
  }

  // 出库成功回调
  const handleOutboundSuccess = () => {
    refreshData()
  }

  // 加载机构选项
  const loadOrgOptions = async () => {
    try {
      const response = await getBizOrgList({ pageSize: 1000 })
      if (response.code === 200) {
        orgOptions.value = response.data.list.map(org => ({
          label: org.orgName,
          value: org.id
        }))
      }
    } catch (error) {
      console.error('加载机构选项失败:', error)
    }
  }

  // 窗口大小变化处理
  const handleResize = () => {
    windowHeight.value = window.innerHeight
    windowWidth.value = window.innerWidth
  }

  // 初始化
  const initialize = async () => {
    window.addEventListener('resize', handleResize)
    await loadOrgOptions()
    await refreshData()
  }

  // 清理
  const cleanup = () => {
    window.removeEventListener('resize', handleResize)
  }

  return {
    // 图标
    SearchOutline: SearchOutlineIcon,
    RefreshOutline: RefreshOutlineIcon,
    AddOutline: AddOutlineIcon,
    CreateOutline: CreateOutlineIcon,
    CopyOutline,
    CloseOutline,
    Building,

    // 响应式数据
    tableRef,
    loading,
    filterForm,
    pagination,
    showOrgSelector,
    selectedOrgs,
    selectedRowKeys,
    showAddModal,
    showOutboundModal,
    currentOutboundRow,
    orgOptions,
    stockStatusOptions,

    // 计算属性
    tableMaxHeight,
    filteredData,
    columns,
    selectedOrgText,

    // 业务方法
    handleSearch,
    handleKeywordsClear,
    refreshData,
    handlePageChange,
    handlePageSizeChange,
    handleSelectionChange,
    getRowKey,
    handleOrgSelect,
    handleOrgCancel,
    clearOrgSelection,
    removeOrg,
    batchUpdateStatus,
    handleOutbound,
    handleOutboundSuccess,
    handleStockStatusChange,

    // 生命周期方法
    initialize,
    cleanup
  }
}
