<template>
  <div class="new-car-sales-flow">
    <n-card title="新车销售流程" class="main-card">
      <template #header-extra>
        <n-button @click="goBack" quaternary>
          <template #icon>
            <n-icon>
              <ArrowBackOutline />
            </n-icon>
          </template>
          返回
        </n-button>
      </template>

      <div class="flow-form-container">
        <n-steps :current="currentStep" :status="stepStatus">
          <n-step title="客户信息" description="录入客户基本信息" />
          <n-step title="车辆选择" description="选择车型和配置" />
          <n-step title="价格确认" description="确认销售价格" />
          <n-step title="合同签署" description="签署销售合同" />
          <n-step title="交车确认" description="完成交车手续" />
        </n-steps>

        <div class="step-content">
          <!-- 客户信息步骤 -->
          <div v-if="currentStep === 1" class="step-panel">
            <n-form
              :model="formData.customer"
              label-placement="left"
              label-width="120px"
            >
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="客户姓名" required>
                    <n-input
                      v-model:value="formData.customer.name"
                      placeholder="请输入客户姓名"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="联系电话" required>
                    <n-input
                      v-model:value="formData.customer.phone"
                      placeholder="请输入联系电话"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="身份证号" required>
                    <n-input
                      v-model:value="formData.customer.idCard"
                      placeholder="请输入身份证号"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="客户地址">
                    <n-input
                      v-model:value="formData.customer.address"
                      placeholder="请输入客户地址"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-form>
          </div>

          <!-- 车辆选择步骤 -->
          <div v-if="currentStep === 2" class="step-panel">
            <n-form
              :model="formData.vehicle"
              label-placement="left"
              label-width="120px"
            >
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="车型品牌" required>
                    <n-select
                      v-model:value="formData.vehicle.brand"
                      placeholder="请选择品牌"
                      :options="brandOptions"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="车型型号" required>
                    <n-select
                      v-model:value="formData.vehicle.model"
                      placeholder="请选择型号"
                      :options="modelOptions"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="车身颜色" required>
                    <n-select
                      v-model:value="formData.vehicle.color"
                      placeholder="请选择颜色"
                      :options="colorOptions"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="配置等级" required>
                    <n-select
                      v-model:value="formData.vehicle.config"
                      placeholder="请选择配置"
                      :options="configOptions"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-form>
          </div>

          <!-- 价格确认步骤 -->
          <div v-if="currentStep === 3" class="step-panel">
            <n-form
              :model="formData.price"
              label-placement="left"
              label-width="120px"
            >
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="指导价格">
                    <n-input-number
                      v-model:value="formData.price.listPrice"
                      :precision="2"
                      readonly
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="优惠金额">
                    <n-input-number
                      v-model:value="formData.price.discount"
                      :precision="2"
                      placeholder="请输入优惠金额"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="实际成交价">
                    <n-input-number
                      v-model:value="actualPrice"
                      :precision="2"
                      readonly
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="付款方式">
                    <n-select
                      v-model:value="formData.price.paymentMethod"
                      placeholder="请选择付款方式"
                      :options="paymentOptions"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-form>
          </div>

          <!-- 其他步骤的占位内容 -->
          <div v-if="currentStep === 4" class="step-panel">
            <n-result
              status="info"
              title="合同签署"
              description="此步骤需要客户到店签署正式销售合同"
            />
          </div>

          <div v-if="currentStep === 5" class="step-panel">
            <n-result
              status="success"
              title="交车确认"
              description="完成车辆交付和相关手续办理"
            />
          </div>
        </div>

        <div class="step-actions">
          <n-space>
            <n-button v-if="currentStep > 1" @click="prevStep">上一步</n-button>
            <n-button v-if="currentStep < 5" type="primary" @click="nextStep"
              >下一步</n-button
            >
            <n-button
              v-if="currentStep === 5"
              type="success"
              @click="submitFlow"
              >完成流程</n-button
            >
          </n-space>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useMessage } from "naive-ui";
import {
  NCard,
  NButton,
  NIcon,
  NSteps,
  NStep,
  NForm,
  NFormItem,
  NGrid,
  NGridItem,
  NInput,
  NInputNumber,
  NSelect,
  NSpace,
  NResult,
} from "naive-ui";
import { ArrowBackOutline } from "@vicons/ionicons5";

// 定义事件
const emit = defineEmits(["back-to-list"]);

const message = useMessage();

// 当前步骤
const currentStep = ref(1);
const stepStatus = ref("process");

// 表单数据
const formData = ref({
  customer: {
    name: "",
    phone: "",
    idCard: "",
    address: "",
  },
  vehicle: {
    brand: null,
    model: null,
    color: null,
    config: null,
  },
  price: {
    listPrice: 280000,
    discount: 0,
    paymentMethod: null,
  },
});

// 选项数据
const brandOptions = ref([
  { label: "奔驰", value: "mercedes" },
  { label: "宝马", value: "bmw" },
  { label: "奥迪", value: "audi" },
]);

const modelOptions = ref([
  { label: "C级", value: "c-class" },
  { label: "E级", value: "e-class" },
  { label: "S级", value: "s-class" },
]);

const colorOptions = ref([
  { label: "珍珠白", value: "white" },
  { label: "曜石黑", value: "black" },
  { label: "银河银", value: "silver" },
  { label: "天际蓝", value: "blue" },
]);

const configOptions = ref([
  { label: "标准版", value: "standard" },
  { label: "豪华版", value: "luxury" },
  { label: "运动版", value: "sport" },
]);

const paymentOptions = ref([
  { label: "全款", value: "cash" },
  { label: "分期付款", value: "installment" },
  { label: "银行贷款", value: "loan" },
]);

// 计算实际成交价
const actualPrice = computed(() => {
  return formData.value.price.listPrice - (formData.value.price.discount || 0);
});

// 步骤操作
const nextStep = () => {
  if (currentStep.value < 5) {
    currentStep.value++;
  }
};

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const submitFlow = () => {
  message.success("新车销售流程已完成！");
  console.log("提交的表单数据:", formData.value);
};

const goBack = () => {
  emit("back-to-list");
};
</script>

<style scoped>
.new-car-sales-flow {
  width: 100%;
  height: 100vh;
  padding: 16px;
  background-color: #f5f5f5;
}

.main-card {
  height: calc(100vh - 32px);
}

.main-card :deep(.n-card__content) {
  padding: 24px;
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.flow-form-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 24px;
}

.step-content {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
}

.step-panel {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e0e0e6;
}

.step-actions {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #e0e0e6;
  background: #fff;
  border-radius: 0 0 8px 8px;
}
</style>
