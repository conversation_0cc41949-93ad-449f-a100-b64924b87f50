<template>
  <div class="sales-page">
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增销售
        </n-button>
        <n-button
          type="error"
          @click="batchDelete"
          :disabled="!selectedRows.length"
          round
        >
          <template #icon>
            <n-icon><TrashOutline /></n-icon>
          </template>
          批量删除
        </n-button>
      </n-space>
      <n-space>
        <n-date-picker
          v-model:value="dateRange"
          type="daterange"
          clearable
          style="width: 240px"
        />
        <n-input
          v-model:value="searchKeyword"
          placeholder="输入关键词搜索"
          clearable
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
        <n-button type="info" @click="handleSearch"> 搜索 </n-button>
      </n-space>
    </n-space>

    <!-- 销售统计卡片 -->
    <n-grid :cols="4" :x-gap="16" class="stat-cards">
      <n-grid-item>
        <n-card size="small">
          <template #header>
            <n-space align="center">
              <n-icon size="20" color="#2080f0">
                <CartOutline />
              </n-icon>
              <span>今日销售额</span>
            </n-space>
          </template>
          <div class="stat-value">¥{{ formatNumber(todaySales) }}</div>
          <div class="stat-compare">
            <span>较昨日</span>
            <span :class="todayCompare >= 0 ? 'up' : 'down'">
              {{ todayCompare >= 0 ? "+" : "" }}{{ todayCompare }}%
            </span>
          </div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card size="small">
          <template #header>
            <n-space align="center">
              <n-icon size="20" color="#18a058">
                <BarChartOutline />
              </n-icon>
              <span>本月销售额</span>
            </n-space>
          </template>
          <div class="stat-value">¥{{ formatNumber(monthSales) }}</div>
          <div class="stat-compare">
            <span>较上月</span>
            <span :class="monthCompare >= 0 ? 'up' : 'down'">
              {{ monthCompare >= 0 ? "+" : "" }}{{ monthCompare }}%
            </span>
          </div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card size="small">
          <template #header>
            <n-space align="center">
              <n-icon size="20" color="#d03050">
                <PeopleOutline />
              </n-icon>
              <span>客户数量</span>
            </n-space>
          </template>
          <div class="stat-value">{{ formatNumber(customerCount) }}</div>
          <div class="stat-compare">
            <span>较上月</span>
            <span :class="customerCompare >= 0 ? 'up' : 'down'">
              {{ customerCompare >= 0 ? "+" : "" }}{{ customerCompare }}%
            </span>
          </div>
        </n-card>
      </n-grid-item>
      <n-grid-item>
        <n-card size="small">
          <template #header>
            <n-space align="center">
              <n-icon size="20" color="#f0a020">
                <PricetagsOutline />
              </n-icon>
              <span>销售订单数</span>
            </n-space>
          </template>
          <div class="stat-value">{{ formatNumber(orderCount) }}</div>
          <div class="stat-compare">
            <span>较上月</span>
            <span :class="orderCompare >= 0 ? 'up' : 'down'">
              {{ orderCompare >= 0 ? "+" : "" }}{{ orderCompare }}%
            </span>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 销售列表 -->
    <n-data-table
      ref="tableRef"
      :columns="columns"
      :data="filteredData"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row) => row.id"
      @update:checked-row-keys="handleSelectionChange"
      @update:page="handlePageChange"
    />

    <!-- 新增/编辑对话框 -->
    <n-modal
      v-model:show="dialogVisible"
      :title="dialogTitle"
      preset="card"
      :style="{ width: '600px' }"
      :mask-closable="false"
    >
      <n-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="订单编号" path="orderNo">
              <n-input
                v-model:value="form.orderNo"
                placeholder="系统自动生成"
                disabled
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="客户名称" path="customerName">
              <n-input
                v-model:value="form.customerName"
                placeholder="请输入客户名称"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="销售日期" path="saleDate">
              <n-date-picker
                v-model:value="form.saleDate"
                type="date"
                clearable
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="销售员" path="salesAgentName">
              <n-input
                v-model:value="form.salesAgentName"
                placeholder="请输入销售员姓名"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-form-item label="商品信息" path="products">
          <n-dynamic-input
            v-model:value="form.products"
            :on-create="
              () => ({ productName: '', quantity: 1, price: 0, amount: 0 })
            "
          >
            <template #default="{ value, index }">
              <div style="display: flex; align-items: center; width: 100%">
                <n-input
                  v-model:value="value.productName"
                  placeholder="商品名称"
                  style="width: 40%"
                  @update:value="updateProductAmount(index)"
                />
                <n-input-number
                  v-model:value="value.quantity"
                  placeholder="数量"
                  :min="1"
                  style="width: 20%; margin: 0 8px"
                  @update:value="updateProductAmount(index)"
                />
                <n-input-number
                  v-model:value="value.price"
                  placeholder="单价"
                  :min="0"
                  :precision="2"
                  style="width: 20%; margin-right: 8px"
                  @update:value="updateProductAmount(index)"
                />
                <n-input-number
                  v-model:value="value.amount"
                  placeholder="金额"
                  :min="0"
                  :precision="2"
                  disabled
                  style="width: 20%"
                />
              </div>
            </template>
          </n-dynamic-input>
        </n-form-item>
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="支付方式" path="paymentMethod">
              <n-select
                v-model:value="form.paymentMethod"
                :options="paymentOptions"
                placeholder="请选择支付方式"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="订单状态" path="status">
              <n-select
                v-model:value="form.status"
                :options="statusOptions"
                placeholder="请选择订单状态"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-form-item label="总金额" path="totalAmount">
          <n-input-number
            v-model:value="form.totalAmount"
            :min="0"
            :precision="2"
            disabled
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="form.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="dialogVisible = false">取消</n-button>
          <n-button type="primary" @click="handleSave">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, h, computed, reactive, watch } from "vue";
import { useDialog, useMessage } from "naive-ui";
import {
  RefreshOutline,
  AddOutline,
  TrashOutline,
  PencilOutline,
  SearchOutline,
  CartOutline,
  BarChartOutline,
  PeopleOutline,
  PricetagsOutline,
} from "@vicons/ionicons5";
import SearchIcon from "@/components/icons/SearchIcon.vue";
import {
  NButton,
  NSpace,
  NIcon,
  NDataTable,
  NModal,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NSelect,
  NDatePicker,
  NGrid,
  NGridItem,
  NCard,
  NDynamicInput,
} from "naive-ui";

// 模拟数据
const mockData = [
  {
    id: 1,
    orderNo: "**********",
    customerName: "张三",
    saleDate: new Date("2023-01-15").getTime(),
    salesAgentName: "李四",
    products: [
      { productName: "商品A", quantity: 2, price: 199.99, amount: 399.98 },
      { productName: "商品B", quantity: 1, price: 299.99, amount: 299.99 },
    ],
    paymentMethod: "微信支付",
    status: "已完成",
    totalAmount: 699.97,
    remark: "客户很满意",
    createTime: "2023-01-15 10:30:00",
    updateTime: "2023-01-15 10:30:00",
  },
  {
    id: 2,
    orderNo: "SO20230002",
    customerName: "王五",
    saleDate: new Date("2023-02-20").getTime(),
    salesAgentName: "赵六",
    products: [
      { productName: "商品C", quantity: 5, price: 9.99, amount: 49.95 },
    ],
    paymentMethod: "支付宝",
    status: "已完成",
    totalAmount: 49.95,
    remark: "",
    createTime: "2023-02-20 14:15:00",
    updateTime: "2023-02-20 14:15:00",
  },
  {
    id: 3,
    orderNo: "SO20230003",
    customerName: "刘七",
    saleDate: new Date("2023-03-10").getTime(),
    salesAgentName: "李四",
    products: [
      { productName: "商品A", quantity: 1, price: 199.99, amount: 199.99 },
      { productName: "商品B", quantity: 2, price: 299.99, amount: 599.98 },
      { productName: "商品C", quantity: 3, price: 9.99, amount: 29.97 },
    ],
    paymentMethod: "现金",
    status: "已完成",
    totalAmount: 829.94,
    remark: "大客户",
    createTime: "2023-03-10 09:45:00",
    updateTime: "2023-03-10 09:45:00",
  },
];

// 状态变量
const tableRef = ref(null);
const formRef = ref(null);
const loading = ref(false);
const dialogVisible = ref(false);
const dialogTitle = ref("新增销售");
const isEdit = ref(false);
const selectedRows = ref([]);
const searchKeyword = ref("");
const dateRange = ref(null);
const salesData = ref([...mockData]);

// 统计数据
const todaySales = ref(12580.5);
const todayCompare = ref(5.8);
const monthSales = ref(358620.75);
const monthCompare = ref(-2.3);
const customerCount = ref(156);
const customerCompare = ref(12.5);
const orderCount = ref(287);
const orderCompare = ref(8.2);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  },
});

// 表单数据
const form = ref({
  id: null,
  orderNo: "",
  customerName: "",
  saleDate: null,
  salesAgentName: "",
  products: [],
  paymentMethod: null,
  status: "已完成",
  totalAmount: 0,
  remark: "",
});

// 表单验证规则
const rules = {
  customerName: [
    { required: true, message: "请输入客户名称", trigger: "blur" },
  ],
  saleDate: [
    {
      required: true,
      type: "number",
      message: "请选择销售日期",
      trigger: "change",
    },
  ],
  salesAgentName: [
    { required: true, message: "请输入销售员姓名", trigger: "blur" },
  ],
  products: [
    {
      required: true,
      type: "array",
      min: 1,
      message: "请至少添加一个商品",
      trigger: "change",
    },
  ],
  paymentMethod: [
    { required: true, message: "请选择支付方式", trigger: "change" },
  ],
  status: [{ required: true, message: "请选择订单状态", trigger: "change" }],
};

// 支付方式选项
const paymentOptions = [
  { label: "微信支付", value: "微信支付" },
  { label: "支付宝", value: "支付宝" },
  { label: "现金", value: "现金" },
  { label: "银行转账", value: "银行转账" },
  { label: "信用卡", value: "信用卡" },
];

// 订单状态选项
const statusOptions = [
  { label: "待付款", value: "待付款" },
  { label: "已付款", value: "已付款" },
  { label: "已发货", value: "已发货" },
  { label: "已完成", value: "已完成" },
  { label: "已取消", value: "已取消" },
];

// 工具实例
const dialog = useDialog();
const message = useMessage();

// 表格列配置
const columns = [
  { type: "selection", width: 50 },
  { title: "ID", key: "id", width: 60 },
  { title: "订单编号", key: "orderNo", width: 120 },
  { title: "客户名称", key: "customerName", width: 120 },
  {
    title: "销售日期",
    key: "saleDate",
    width: 120,
    sorter: (a, b) => a.saleDate - b.saleDate,
    render(row) {
      return h("span", null, new Date(row.saleDate).toLocaleDateString());
    },
  },
  { title: "销售员", key: "salesAgentName", width: 100 },
  {
    title: "商品数量",
    key: "productCount",
    width: 100,
    render(row) {
      return h("span", null, row.products.length);
    },
  },
  {
    title: "总金额",
    key: "totalAmount",
    width: 120,
    sorter: (a, b) => a.totalAmount - b.totalAmount,
    render(row) {
      return h(
        "span",
        { style: { fontWeight: "bold" } },
        `¥${row.totalAmount.toFixed(2)}`
      );
    },
  },
  {
    title: "支付方式",
    key: "paymentMethod",
    width: 100,
  },
  {
    title: "订单状态",
    key: "status",
    width: 100,
    render(row) {
      const statusColorMap = {
        待付款: "#f0a020",
        已付款: "#2080f0",
        已发货: "#18a058",
        已完成: "#18a058",
        已取消: "#d03050",
      };
      return h(
        "span",
        { style: { color: statusColorMap[row.status] || "#333" } },
        row.status
      );
    },
  },
  { title: "备注", key: "remark", ellipsis: true },
  {
    title: "创建时间",
    key: "createTime",
    width: 180,
  },
  {
    title: "操作",
    key: "actions",
    width: 180,
    fixed: "right",
    render(row) {
      return h(NSpace, { justify: "center", size: "small" }, [
        h(
          NButton,
          {
            size: "small",
            type: "info",
            onClick: () => viewOrderDetails(row),
          },
          {
            default: () => "查看",
            icon: () => h(SearchIcon, { size: 16, color: "#18a058" }),
          }
        ),
        h(
          NButton,
          {
            size: "small",
            type: "primary",
            onClick: () => handleEdit(row),
          },
          {
            default: () => "编辑",
            icon: () => h(NIcon, null, { default: () => h(PencilOutline) }),
          }
        ),
        h(
          NButton,
          {
            size: "small",
            type: "error",
            onClick: () => handleDelete(row),
          },
          {
            default: () => "删除",
            icon: () => h(NIcon, null, { default: () => h(TrashOutline) }),
          }
        ),
      ]);
    },
  },
];

// 根据搜索条件过滤数据
const filteredData = computed(() => {
  let result = [...salesData.value];

  // 按关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(
      (item) =>
        item.orderNo.toLowerCase().includes(keyword) ||
        item.customerName.toLowerCase().includes(keyword) ||
        item.salesAgentName.toLowerCase().includes(keyword) ||
        item.paymentMethod.toLowerCase().includes(keyword) ||
        item.status.toLowerCase().includes(keyword) ||
        item.remark?.toLowerCase().includes(keyword)
    );
  }

  // 按日期范围过滤
  if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
    const startDate = new Date(dateRange.value[0]).setHours(0, 0, 0, 0);
    const endDate = new Date(dateRange.value[1]).setHours(23, 59, 59, 999);
    result = result.filter((item) => {
      const saleDate = new Date(item.saleDate).getTime();
      return saleDate >= startDate && saleDate <= endDate;
    });
  }

  return result;
});

// 页面加载时获取数据
onMounted(() => {
  fetchData();
});

// 获取销售数据
const fetchData = () => {
  loading.value = true;
  // 模拟API请求
  setTimeout(() => {
    salesData.value = [...mockData];
    loading.value = false;
  }, 500);
};

// 刷新数据
const refreshData = () => {
  fetchData();
  message.success("数据已刷新");
};

// 显示新增对话框
const showAddDialog = () => {
  dialogTitle.value = "新增销售";
  isEdit.value = false;
  form.value = {
    id: null,
    orderNo: generateOrderNo(),
    customerName: "",
    saleDate: Date.now(),
    salesAgentName: "",
    products: [{ productName: "", quantity: 1, price: 0, amount: 0 }],
    paymentMethod: null,
    status: "已完成",
    totalAmount: 0,
    remark: "",
  };
  dialogVisible.value = true;
};

// 生成订单编号
const generateOrderNo = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  return `SO${year}${month}${day}${random}`;
};

// 查看订单详情
const viewOrderDetails = (row) => {
  // 这里可以实现查看详情的逻辑，例如打开一个只读的详情对话框
  message.info(`查看订单 ${row.orderNo} 的详情`);
};

// 处理编辑
const handleEdit = (row) => {
  dialogTitle.value = "编辑销售";
  isEdit.value = true;
  // 深拷贝数据，避免直接修改原数据
  form.value = JSON.parse(JSON.stringify(row));
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = (row) => {
  dialog.warning({
    title: "警告",
    content: `确定要删除订单 "${row.orderNo}" 吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: () => {
      // 模拟删除操作
      salesData.value = salesData.value.filter((item) => item.id !== row.id);
      message.success("删除成功");
    },
  });
};

// 批量删除
const batchDelete = () => {
  if (selectedRows.value.length === 0) {
    message.warning("请先选择要删除的订单");
    return;
  }

  dialog.warning({
    title: "警告",
    content: `确定要删除选中的 ${selectedRows.value.length} 个订单吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: () => {
      // 模拟批量删除操作
      const ids = selectedRows.value.map((row) => row.id);
      salesData.value = salesData.value.filter(
        (item) => !ids.includes(item.id)
      );
      selectedRows.value = [];
      message.success("批量删除成功");
    },
  });
};

// 更新商品金额
const updateProductAmount = (index) => {
  if (!form.value.products[index]) return;

  const product = form.value.products[index];
  product.amount = (product.quantity || 0) * (product.price || 0);

  // 更新总金额
  calculateTotalAmount();
};

// 计算总金额
const calculateTotalAmount = () => {
  form.value.totalAmount = form.value.products.reduce((sum, product) => {
    return sum + (product.amount || 0);
  }, 0);
};

// 监听产品变化，自动计算总金额
watch(
  () => form.value.products,
  () => {
    calculateTotalAmount();
  },
  { deep: true }
);

// 处理保存
const handleSave = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) return;

    // 模拟保存操作
    if (isEdit.value) {
      // 更新
      const index = salesData.value.findIndex(
        (item) => item.id === form.value.id
      );
      if (index !== -1) {
        salesData.value[index] = {
          ...form.value,
          updateTime: new Date().toLocaleString(),
        };
        message.success("更新成功");
      }
    } else {
      // 新增
      const newId = Math.max(...salesData.value.map((item) => item.id), 0) + 1;
      const now = new Date().toLocaleString();
      salesData.value.push({
        ...form.value,
        id: newId,
        createTime: now,
        updateTime: now,
      });
      message.success("添加成功");
    }

    dialogVisible.value = false;
  });
};

// 处理选择变化
const handleSelectionChange = (keys) => {
  selectedRows.value = salesData.value.filter((item) => keys.includes(item.id));
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page;
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
};

// 格式化数字
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};
</script>

<style scoped>
.sales-page {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  margin-bottom: 16px;
}

.stat-cards {
  margin-bottom: 16px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin: 8px 0;
}

.stat-compare {
  font-size: 12px;
  color: #999;
  display: flex;
  justify-content: space-between;
}

.up {
  color: #18a058;
}

.down {
  color: #d03050;
}

.n-data-table {
  flex: 1;
}
</style>
