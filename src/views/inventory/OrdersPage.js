import { ref, reactive, computed, onMounted, onUnmounted, markRaw, h } from 'vue'
import { useRouter } from 'vue-router'
import { useDialog } from 'naive-ui'
import messages from '@/utils/messages'
import vehicleOrderApi from '@/api/vehicleOrder'
import {
  SearchOutline,
  RefreshOutline,
  AddOutline,
  CreateOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  ArchiveOutline,
  CarOutline,
  CopyOutline,
  BusinessOutline
} from '@vicons/ionicons5'
import SearchIcon from '@/components/icons/SearchIcon.vue'
import { dateRangeOptions, getDateRangeParams, handleDateRangeChange as handleDateChange, handleCustomDateChange as handleCustomDate } from '@/utils/dateRange'
import { createOrdersTableColumns } from './OrdersPageColumns.js'
import { orderStatusUtils, useDictOptions } from '@/utils/dictUtils'

// 组件导入
import OrderDetailModal from '@/components/orders/OrderDetailModal.vue'
import OrderEditModal from '@/components/orders/OrderEditModal.vue'
import DepositOrderModal from '@/components/orders/DepositOrderModal.vue'
import BizOrgSelector from '@/components/bizOrg/BizOrgSelector.vue'

/**
 * 订单页面逻辑
 * @returns {Object} 页面所需的响应式数据和方法
 */
export function useOrdersPage() {
  // 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
  const SearchOutlineIcon = markRaw(SearchOutline)
  const RefreshOutlineIcon = markRaw(RefreshOutline)
  const AddOutlineIcon = markRaw(AddOutline)
  const SearchIconComponent = markRaw(SearchIcon)
  const CreateOutlineIcon = markRaw(CreateOutline)
  const CheckmarkCircleOutlineIcon = markRaw(CheckmarkCircleOutline)
  const CloseCircleOutlineIcon = markRaw(CloseCircleOutline)
  const ArchiveOutlineIcon = markRaw(ArchiveOutline)
  const CarOutlineIcon = markRaw(CarOutline)
  const CopyOutlineIcon = markRaw(CopyOutline)
  const BusinessOutlineIcon = markRaw(BusinessOutline)

  // 路由和对话框
  const router = useRouter()
  const dialog = useDialog()

  // 状态变量
  const tableRef = ref(null)
  const orderEditModalRef = ref(null)
  const depositOrderModalRef = ref(null)
  const loading = ref(false)
  const dialogVisible = ref(false)
  const depositDialogVisible = ref(false)
  const dialogTitle = ref('新增订单')
  const depositDialogTitle = ref('新增定金订单')
  const isEdit = ref(false)
  const isDepositEdit = ref(false)
  const selectedRows = ref([])
  const currentDepositEditData = ref(null)

  // 业务机构选择器相关状态
  const showOrgSelector = ref(false)



  /**
   * 订单状态选项 - 使用响应式字典数据
   */
  const { options: allOrderStatusOptions } = useDictOptions('order_status', true)

  const orderStatusOptions = computed(() => {
    // 过滤掉待处理(pending)和已归档(archived)状态
    return allOrderStatusOptions.value.filter(option =>
      option.value === null || !['archived'].includes(option.value)
    )
  })

  // 筛选表单
  const filterForm = reactive({
    dateRange: null,
    customDateRange: null,
    vehicleCategory: null,
    invoiceOrgs: [], // 改为数组格式，支持多选
    minAmount: null,
    maxAmount: null,
    keywords: '',
    orderStatus: null
  })

  // 数据列表
  const ordersData = ref([])

  // 窗口高度响应式变量
  const windowHeight = ref(window.innerHeight)

  // 计算表格最大高度 - 用于虚拟滚动（参考StockDetailsPage的成功实现）
  const tableMaxHeight = computed(() => {
    const screenHeight = windowHeight.value

    // 基础组件高度配置（与StockDetailsPage保持一致）
    const pagepadding = 32 // 页面上下padding
    const filterHeight = 140 // 筛选区域高度
    const toolbarHeight = 60 // 工具栏高度
    const paginationHeight = 50 // 分页区域高度
    const margin = 20 // 额外边距

    // 计算表格容器的可用高度
    const calculatedHeight = screenHeight - pagepadding - filterHeight - toolbarHeight - paginationHeight - margin

    // 根据屏幕尺寸进行优化（与StockDetailsPage保持一致）
    let finalHeight
    if (screenHeight >= 1440) {
      // 大屏幕 (27寸+)
      finalHeight = Math.max(calculatedHeight, 700)
    } else if (screenHeight >= 1080) {
      // 24寸屏幕
      finalHeight = Math.max(calculatedHeight, 500)
    } else if (screenHeight >= 768) {
      // 中等屏幕
      finalHeight = Math.max(calculatedHeight, 400)
    } else {
      // 小屏幕 (13寸等)
      finalHeight = Math.max(calculatedHeight, 300)
    }

    // 关键：减去180px（比StockDetailsPage少减5px，给最后一行更多空间）
    return finalHeight - 195
  })

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 100, // 增加默认页面大小以便测试虚拟滚动
    pageCount: 1,
    showSizePicker: true,
    pageSizes: [20, 50, 100, 200],
    itemCount: 0,
    showQuickJumper: false
  })

  // 计算属性：选中机构的显示文本
  const selectedOrgText = computed(() => {
    if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
      if (filterForm.invoiceOrgs.length === 1) {
        return filterForm.invoiceOrgs[0].orgName
      } else {
        return `已选择 ${filterForm.invoiceOrgs.length} 个机构`
      }
    }
    return "选择销售单位"
  })

  /**
   * 复制文本到剪贴板
   * @param {string} text - 要复制的文本
   */
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        messages.success('已复制到剪贴板')
      })
      .catch(err => {
        console.error('复制失败:', err)
        messages.error('复制失败')
      })
  }

  /**
   * 处理查看订单详情
   * @param {number} id - 订单ID
   */
  const handleView = (id) => {
    currentDetailId.value = id
    detailDialogVisible.value = true
  }

  /**
   * 处理编辑销售订单
   * @param {number} id - 订单ID
   */
  const handleEdit = async (id) => {
    try {
      loading.value = true

      // 调用API获取详细数据
      const response = await vehicleOrderApi.getOrderDetail(id)

      if (response.code === 200) {
        const apiData = response.data

        isEdit.value = true

        // 普通销售订单
        dialogTitle.value = '编辑销售订单'

        // 设置表单数据
        if (orderEditModalRef.value) {
          orderEditModalRef.value.setFormData(apiData)
        }

        dialogVisible.value = true
      } else {
        messages.error(response.message || '获取订单数据失败')
      }
    } catch (error) {
      console.error('获取订单数据失败:', error)
      messages.error('获取订单数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  /**
   * 处理编辑定金订单
   * @param {number} id - 订单ID
   */
  const handleDepositEdit = async (id) => {
    try {
      loading.value = true

      // 调用API获取详细数据
      const response = await vehicleOrderApi.getOrderDetail(id)

      if (response.code === 200) {
        const apiData = response.data

        isDepositEdit.value = true

        // 定金订单
        depositDialogTitle.value = '编辑定金订单'

        // 设置当前编辑数据
        currentDepositEditData.value = apiData

        depositDialogVisible.value = true
      } else {
        messages.error(response.message || '获取订单数据失败')
      }
    } catch (error) {
      console.error('获取订单数据失败:', error)
      messages.error('获取订单数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  /**
   * 处理订单状态变更
   * @param {number} id - 订单ID
   * @param {string} newStatus - 新状态
   */
  const handleStatusChange = async (id, newStatus) => {
    try {
      loading.value = true

      // 调用API更新订单状态
      const response = await vehicleOrderApi.updateOrderStatus(id, newStatus)

      if (response.code === 200) {
        messages.success(`订单状态已更新为${getStatusText(newStatus)}`)
        // 刷新数据列表
        refreshData()
      } else {
        messages.error(response.message || '更新订单状态失败')
      }
    } catch (error) {
      console.error('更新订单状态失败:', error)
      messages.error('更新订单状态失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取状态文本 - 从字典数据获取
   * @param {string} status - 状态值
   * @returns {string} 状态文本
   */
  const getStatusText = (status) => orderStatusUtils.getLabel(status)

  /**
   * 处理取消订单 - 专门用于定金订单
   * @param {number} id - 订单ID
   * @param {string} orderSn - 订单编号
   */
  const handleCancelOrder = (id, orderSn) => {
    dialog.warning({
      title: '取消订单',
      content: () => h('div', [
        h('div', `确定要取消订单 "${orderSn}" 吗？`),
        h('div', { style: 'margin-top: 8px; color: #f56c6c;' }, '取消定金订单后将产生财务应付账款。'),
      ]),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          loading.value = true

          // 调用API取消订单
          const response = await vehicleOrderApi.updateOrderStatus(id, 'canceled')

          if (response.code === 200) {
            messages.success('订单已取消')
            // 刷新数据列表
            refreshData()
          } else {
            messages.error(response.message || '取消订单失败')
          }
        } catch (error) {
          console.error('取消订单失败:', error)
          messages.error('取消订单失败，请稍后重试')
        } finally {
          loading.value = false
        }
      }
    })
  }

  /**
   * 刷新数据列表
   */
  const refreshData = async () => {
    loading.value = true
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: pagination.pageSize
      }

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords
      }

      // 处理日期范围
      if (filterForm.dateRange) {
        const dateRange = getDateRangeParams(filterForm.dateRange, filterForm.customDateRange)
        if (dateRange.startDate) params.startDate = dateRange.startDate
        if (dateRange.endDate) params.endDate = dateRange.endDate
      }

      // 处理车辆类别
      if (filterForm.vehicleCategory) {
        params.brand = filterForm.vehicleCategory
      }

      // 处理销售单位
      if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
        params.salesOrgIds = filterForm.invoiceOrgs.map(org => org.id).join(',')
      }

      // 处理订单状态
      if (filterForm.orderStatus) {
        params.orderStatus = filterForm.orderStatus
      }

      try {
        // 调用API获取数据
        const response = await vehicleOrderApi.getOrderList(params)

        if (response.code === 200) {
          // 直接使用返回的数据列表
          ordersData.value = response.data.list

          // 更新分页信息
          pagination.itemCount = response.data.total
          pagination.pageCount = response.data.pages

          // 确保当前页码与API返回的一致
          if (pagination.page !== response.data.pageNum) {
            pagination.page = response.data.pageNum
          }



          // 如果当前页大于总页数且总页数不为0，则跳转到最后一页
          if (pagination.page > response.data.pages && response.data.pages > 0) {
            pagination.page = response.data.pages
            refreshData()
            return
          }
        } else {
          messages.error(response.message || '数据加载失败')
        }
      } catch (apiError) {
        console.warn('API调用失败，使用模拟数据:', apiError)

        // 如果API调用失败，使用模拟数据进行测试
        // 为了测试虚拟滚动，生成更多数据（特别是在大屏幕上）
        // 在24寸屏幕上，可能需要更多数据才能触发虚拟滚动
        const mockDataCount = Math.max(pagination.pageSize, 500) // 至少生成500条数据
        const mockData = generateMockOrderData(mockDataCount)
        ordersData.value = mockData
        pagination.itemCount = 5000 // 模拟总数，确保有足够数据测试虚拟滚动
        pagination.pageCount = Math.ceil(5000 / pagination.pageSize)

        console.log('模拟数据生成:', {
          pageSize: pagination.pageSize,
          dataLength: mockData.length,
          itemCount: pagination.itemCount,
          pageCount: pagination.pageCount,
          tableMaxHeight: tableMaxHeight.value,
          windowHeight: windowHeight.value
        })

        messages.info('当前使用模拟数据进行展示')
      }
    } catch (error) {
      console.error('获取订单列表失败:', error)
      messages.error('加载数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  /**
   * 生成模拟订单数据
   * @param {number} count - 生成数据条数
   * @returns {Array} 模拟数据数组
   */
  const generateMockOrderData = (count) => {
    const mockData = []
    const brands = ['阿维塔', '深蓝', '引力', '启源', '凯程']
    const statuses = ['pending', 'confirmed', 'delivered', 'canceled']
    const orderTypes = ['normal', 'deposit']

    // 确保生成的数据条数严格等于count参数
    for (let i = 1; i <= count; i++) {
      const id = (pagination.page - 1) * pagination.pageSize + i
      mockData.push({
        id: id,
        orderSn: `SO${String(id).padStart(8, '0')}`,
        dealDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        orderType: orderTypes[Math.floor(Math.random() * orderTypes.length)],
        orderStatus: statuses[Math.floor(Math.random() * statuses.length)],
        salesOrgName: `销售单位${Math.floor(Math.random() * 10) + 1}`,
        salesAgentName: `销售顾问${Math.floor(Math.random() * 20) + 1}`,
        customerName: `客户${Math.floor(Math.random() * 100) + 1}`,
        mobile: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
        brand: brands[Math.floor(Math.random() * brands.length)],
        configName: `配置${Math.floor(Math.random() * 5) + 1}`,
        dealAmount: Math.floor(Math.random() * 50000000) + 10000000 // 100万到600万分
      })
    }

    return mockData
  }

  /**
   * 处理日期范围变化
   * @param {string} value - 日期范围值
   */
  const handleDateRangeChange = (value) => {
    handleDateChange(value, filterForm, handleSearch)
  }

  /**
   * 处理自定义日期变化
   * @param {Array} dates - 日期数组
   */
  const handleCustomDateChange = (dates) => {
    handleCustomDate(dates, handleSearch)
  }

  /**
   * 处理查询
   */
  const handleSearch = () => {
    pagination.page = 1
    refreshData()
  }

  /**
   * 显示新增销售订单对话框
   */
  const showAddDialog = () => {
    // 重置表单
    if (orderEditModalRef.value) {
      orderEditModalRef.value.resetForm()
    }

    // 打开编辑弹窗
    isEdit.value = false
    dialogTitle.value = '新增销售订单'
    dialogVisible.value = true
  }

  /**
   * 显示新增定金订单对话框
   */
  const showAddDepositDialog = () => {
    // 重置表单
    if (depositOrderModalRef.value) {
      depositOrderModalRef.value.resetForm()
    }

    // 打开定金订单弹窗
    isDepositEdit.value = false
    depositDialogTitle.value = '新增定金订单'
    depositDialogVisible.value = true
  }

  /**
   * 导航到定金订单页面
   */
  const navigateToDepositOrderPage = () => {
    router.push('/inventory/deposit-order')
  }

  /**
   * 处理保存成功
   */
  const handleSaveSuccess = () => {
    refreshData() // 刷新数据列表
  }

  /**
   * 处理选择变化
   * @param {Array} keys - 选中的行键
   */
  const handleSelectionChange = (keys) => {
    selectedRows.value = ordersData.value.filter(item => keys.includes(item.id))
  }

  /**
   * 处理页码变化
   * @param {number} page - 页码
   */
  const handlePageChange = (page) => {
    pagination.page = page
    refreshData()
  }

  /**
   * 处理页面大小变化
   * @param {number} pageSize - 页面大小
   */
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    refreshData()
  }

  /**
   * 处理导入成功
   * @param {Object} fileInfo - 文件信息
   */
  const handleImportSuccess = async (fileInfo) => {
    try {
      messages.success(`文件上传成功: ${fileInfo.fileName}`)
      console.log('文件上传路径:', fileInfo.fileKey)

      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', fileInfo.file);

      // 调用订单导入接口
      const response = await vehicleOrderApi.importOrders(formData)

      if (response.code === 200) {
        messages.success('文件解析成功')
        // 刷新数据列表
        refreshData()
      } else {
        messages.error(response.message || '文件解析失败')
      }
    } catch (error) {
      console.error('文件解析失败:', error)
      messages.error('文件解析失败，请检查文件格式是否正确')
    }
  }

  /**
   * 处理导入错误
   * @param {string} errorMsg - 错误信息
   */
  const handleImportError = (errorMsg) => {
    console.error(`导入失败: ${errorMsg}`)
  }

  /**
   * 处理机构选择
   * @param {Array} orgs - 选中的机构列表
   */
  const handleOrgSelect = (orgs) => {
    if (orgs && orgs.length > 0) {
      // 多选模式，保存所有选中的机构
      filterForm.invoiceOrgs = [...orgs]
      handleSearch()
    }
  }

  /**
   * 处理机构选择取消
   */
  const handleOrgCancel = () => {
    showOrgSelector.value = false
  }

  /**
   * 清空机构选择
   */
  const clearOrgSelection = () => {
    filterForm.invoiceOrgs = []
    handleSearch()
  }

  /**
   * 移除单个机构
   * @param {Object} org - 要移除的机构
   */
  const removeOrg = (org) => {
    const index = filterForm.invoiceOrgs.findIndex(item => item.id === org.id)
    if (index > -1) {
      filterForm.invoiceOrgs.splice(index, 1)
      handleSearch()
    }
  }

  // 详情弹窗
  const detailDialogVisible = ref(false)
  const currentDetailId = ref(null)

  // 表格数据
  const filteredData = computed(() => {
    return ordersData.value
  })

  // 表格列配置
  const columns = computed(() => {
    const icons = {
      CopyOutlineIcon,
      SearchIconComponent,
      CreateOutlineIcon,
      CheckmarkCircleOutlineIcon,
      CloseCircleOutlineIcon,
      ArchiveOutlineIcon,
      CarOutlineIcon
    }

    const handlers = {
      copyToClipboard,
      handleView,
      handleEdit,
      handleDepositEdit,
      handleStatusChange,
      handleCancelOrder
    }

    return createOrdersTableColumns(icons, handlers)
  })

  // 表格横向滚动宽度 - 根据实际列宽度计算
  const scrollX = computed(() => {
    // 计算所有列的总宽度（根据OrdersPageColumns.js中的实际宽度）
    // 选择列: 50px + 订单编号: 250px + 销售日期: 250px + 订单类型: 220px +
    // 订单状态: 220px + 销售单位: 320px + 销售顾问: 240px + 客户名称: 140px +
    // 联系电话: 120px + 品牌: 120px + 配置: 200px + 成交金额: 150px + 操作: 160px
    // 总计: 50 + 250 + 250 + 220 + 220 + 320 + 240 + 140 + 120 + 120 + 200 + 150 + 160 = 2440
    return 2440
  })

  // 窗口大小变化监听器
  const handleResize = () => {
    windowHeight.value = window.innerHeight
  }

  // 初始化
  onMounted(() => {
    refreshData()
    window.addEventListener('resize', handleResize, { passive: true })

    // 调试字典数据
    if (import.meta.env.DEV) {
      setTimeout(() => {
        import('@/mock/dictData').then(({ dictOptions }) => {
          console.log('=== 字典数据调试 ===');
          console.log('dictOptions 对象:', dictOptions);
          console.log('sales_store_type 数据:', dictOptions['sales_store_type']);
          console.log('recept_status 数据:', dictOptions['recept_status']);

          // 检查localStorage
          const salesStoreTypeCache = localStorage.getItem('dict_options_sales_store_type');
          const receptStatusCache = localStorage.getItem('dict_options_recept_status');
          console.log('localStorage sales_store_type:', salesStoreTypeCache);
          console.log('localStorage recept_status:', receptStatusCache);

          // 测试字典工具函数
          import('@/utils/dictUtils').then(({ getDictLabel, getDictItem }) => {
            console.log('测试 getDictLabel sales_store_type 4S:', getDictLabel('sales_store_type', '4S'));
            console.log('测试 getDictLabel sales_store_type 3S:', getDictLabel('sales_store_type', '3S'));
            console.log('测试 getDictItem sales_store_type 4S:', getDictItem('sales_store_type', '4S'));
          });
        });
      }, 3000); // 等待3秒确保字典数据加载完成
    }
  })

  // 清理
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })

  return {
    // 图标
    SearchOutlineIcon,
    RefreshOutlineIcon,
    AddOutlineIcon,
    SearchIconComponent,
    CreateOutlineIcon,
    CheckmarkCircleOutlineIcon,
    CloseCircleOutlineIcon,
    ArchiveOutlineIcon,
    CarOutlineIcon,
    CopyOutlineIcon,
    BusinessOutlineIcon,

    // 组件
    OrderDetailModal,
    OrderEditModal,
    DepositOrderModal,
    BizOrgSelector,

    // 路由
    router,

    // 状态
    tableRef,
    orderEditModalRef,
    depositOrderModalRef,
    loading,
    dialogVisible,
    depositDialogVisible,
    dialogTitle,
    depositDialogTitle,
    isEdit,
    isDepositEdit,
    selectedRows,
    currentDepositEditData,
    detailDialogVisible,
    currentDetailId,
    showOrgSelector,

    // 数据
    dateRangeOptions,
    orderStatusOptions,
    filterForm,
    ordersData,
    pagination,
    filteredData,
    columns,
    scrollX,
    tableMaxHeight,
    windowHeight,
    selectedOrgText,
    BusinessOutline,

    // 方法
    refreshData,
    handleDateRangeChange,
    handleCustomDateChange,
    handleSearch,
    showAddDialog,
    showAddDepositDialog,
    navigateToDepositOrderPage,
    copyToClipboard,
    handleView,
    handleEdit,
    handleDepositEdit,
    handleSaveSuccess,
    handleSelectionChange,
    handlePageChange,
    handlePageSizeChange,
    handleImportSuccess,
    handleImportError,
    handleStatusChange,
    handleCancelOrder,
    getStatusText,

    // 机构选择器相关方法
    handleOrgSelect,
    handleOrgCancel,
    clearOrgSelection,
    removeOrg
  }
}
