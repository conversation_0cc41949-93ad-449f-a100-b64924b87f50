<template>
  <div class="stock-details-page">
    <!-- 筛选条件区域 -->
    <n-card class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">车辆品牌</div>
          <div class="filter-options">
            <vehicle-brand-selector
              v-model="filterForm.vehicleCategory"
              @update:model-value="handleSearch"
            />
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增车型
        </n-button>

        <n-button type="info" @click="openSkuSelector()" round>
          <template #icon>
            <n-icon><SearchOutline /></n-icon>
          </template>
          查询车型
        </n-button>

        <file-upload-button
          button-type="info"
          button-text="Excel导入"
          button-mode="standard"
          accept-formats=".xlsx,.xls"
          :max-size="10"
          template-url="/templates/vehicle-stock-template.xlsx"
          :button-style="{ fontWeight: 'bold' }"
          @success="handleImportSuccess"
          @error="handleImportError"
        />

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="请输入品牌/车系/车型"
          style="width: 220px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="filteredData"
          :loading="loading"
          :row-key="(row) => row.id"
          :max-height="tableMaxHeight"
          striped
          size="medium"
          @update:checked-row-keys="handleSelectionChange"
        />
      </div>

      <!-- 分页组件固定在表格底部 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes"
          :item-count="pagination.itemCount"
          :show-size-picker="pagination.showSizePicker"
          :show-quick-jumper="pagination.showQuickJumper"
          size-picker-option-text="条/页"
          page-size-option="每页"
          :prefix="() => `共 ${pagination.itemCount} 条`"
          :display-order="['prefix', 'pages', 'size-picker', 'quick-jumper']"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 详情弹窗 -->
    <sku-detail-modal
      :visible="detailDialogVisible"
      @update:visible="detailDialogVisible = $event"
      :id="currentDetailId"
    />

    <!-- 编辑弹窗 -->
    <sku-edit-modal
      :visible="editDialogVisible"
      @update:visible="editDialogVisible = $event"
      @success="refreshData"
      :id="currentEditId"
    />

    <!-- SKU选择器弹窗 -->
    <vehicle-s-k-u-selector
      :visible="selectorVisible"
      @update:visible="selectorVisible = $event"
      @select="handleSkuSelected"
      :params="selectorParams"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, h } from "vue";
import messages from "@/utils/messages";
import { formatMoney } from "@/utils/money";
import { vehicleBrandUtils } from "@/utils/dictUtils";
import skuApi from "@/api/sku";
import { NIcon, NPagination, useDialog } from "naive-ui";
import {
  SearchOutline,
  RefreshOutline,
  AddOutline,
  CreateOutline,
} from "@vicons/ionicons5";
import SearchIcon from "@/components/icons/SearchIcon.vue";
import FileUploadButton from "@/components/FileUploadButton.vue";
import SkuDetailModal from "@/components/inventory/SkuDetailModal.vue";
import SkuEditModal from "@/components/inventory/SkuEditModal.vue";
import VehicleSKUSelector from "@/components/inventory/VehicleSKUSelector.vue";
import VehicleBrandSelector from "@/components/common/VehicleBrandSelector.vue";

// 消息提示已通过 utils/messages.js 提供

// 初始化对话框
const dialog = useDialog();
window.$dialog = dialog;

// 状态变量
const tableRef = ref(null);
const loading = ref(false);
const selectedRows = ref([]);
const windowHeight = ref(window.innerHeight);

// 筛选表单
const filterForm = reactive({
  vehicleCategory: null,
  keywords: "",
});

// 数据列表
const skuData = ref([]);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [20, 50, 100],
  itemCount: 0, // 总记录数
  showQuickJumper: false,
});

// 表格列配置
const columns = [
  {
    type: "selection",
    width: 50,
  },
  {
    title: "ID",
    key: "id",
    width: 80,
    align: "center",
  },
  {
    title: "品牌",
    key: "brand",
    width: 120,
    align: "center",
  },
  {
    title: "车系",
    key: "series",
    width: 180,
    align: "center",
  },
  {
    title: "配置",
    key: "configName",
    width: 180,
    align: "center",
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "启票价",
    key: "sbPrice",
    width: 120,
    align: "center",
    render: (row) => {
      // 处理启票价格显示逻辑
      if (
        row.sbPrice === null ||
        row.sbPrice === undefined ||
        row.sbPrice === 0
      ) {
        return "未设置";
      }

      const numPrice = Number(row.sbPrice);
      if (isNaN(numPrice)) {
        return "未设置";
      }

      // 检查是否为整数
      if (Number.isInteger(numPrice)) {
        // 整数时不显示小数位
        return `￥${numPrice.toLocaleString("zh-CN")}`;
      } else {
        // 非整数时显示两位小数
        return formatMoney(numPrice, 2, "￥");
      }
    },
  },
  {
    title: "颜色（内/外）",
    key: "colorCode",
    width: 100,
    align: "center",
  },
  {
    title: "操作",
    key: "actions",
    width: 150,
    align: "center",
    render: (row) => {
      return h(
        "div",
        { style: { display: "flex", justifyContent: "center", gap: "12px" } },
        [
          h(
            "div",
            {
              style: {
                cursor: "pointer",
                color: "#2080f0",
                fontSize: "20px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              },
              onClick: () => handleView(row.id),
            },
            [h(SearchIcon, { size: 20, color: "#18a058" })]
          ),
          h(
            "div",
            {
              style: {
                cursor: "pointer",
                color: "#18a058",
                fontSize: "20px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              },
              onClick: () => handleEditSku(row.id),
            },
            [h(NIcon, { size: 20 }, { default: () => h(CreateOutline) })]
          ),
        ]
      );
    },
  },
];

// 表格数据
const filteredData = computed(() => {
  return skuData.value;
});

// 表格最大高度计算 - 普通表格模式
const tableMaxHeight = computed(() => {
  const screenHeight = windowHeight.value;

  // 基础组件高度配置 - 根据实际测量调整
  const pagepadding = 32; // 页面上下padding (16px * 2)
  const filterHeight = 80; // 筛选卡片实际高度
  const toolbarHeight = 60; // 工具栏高度
  const paginationHeight = 40; // 分页区域高度（实际min-height: 40px）
  const margin = 48; // 额外边距和间隙（16px gap * 3）

  // 计算表格容器的可用高度
  const calculatedHeight =
    screenHeight -
    pagepadding -
    filterHeight -
    toolbarHeight -
    paginationHeight -
    margin;

  // 根据屏幕尺寸进行优化
  let finalHeight;
  if (screenHeight >= 1440) {
    // 大屏幕 (27寸+)
    finalHeight = Math.max(calculatedHeight, 700);
  } else if (screenHeight >= 1080) {
    // 24寸屏幕
    finalHeight = Math.max(calculatedHeight, 500);
  } else if (screenHeight >= 768) {
    // 中等屏幕
    finalHeight = Math.max(calculatedHeight, 400);
  } else {
    // 小屏幕
    finalHeight = Math.max(calculatedHeight, 300);
  }

  // 普通表格模式，确保有足够高度显示所有数据
  return finalHeight - 55;
});

// 窗口大小变化监听
const handleResize = () => {
  windowHeight.value = window.innerHeight;
};

// 初始化
onMounted(() => {
  refreshData();
  window.addEventListener("resize", handleResize);
});

// 清理
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});

const refreshData = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      page: pagination.page,
      size: pagination.pageSize,
    };

    // 添加筛选条件
    if (filterForm.keywords) {
      params.keywords = filterForm.keywords;
    }

    // 处理车辆类别 - 将选中的value转换为label传给接口
    if (filterForm.vehicleCategory) {
      // 如果选中的是null（不限），则不传brand参数
      if (filterForm.vehicleCategory === null) {
        // 不传brand参数，查询所有品牌
      } else {
        // 将品牌value转换为label传给接口
        const brandLabel = vehicleBrandUtils.getLabel(
          filterForm.vehicleCategory
        );
        if (brandLabel && brandLabel !== "未知") {
          params.brand = brandLabel;
        }
      }
    }

    // 调用API获取数据
    const response = await skuApi.getSkuList(params);

    if (response.code === 200) {
      // 直接使用返回的数据列表
      skuData.value = response.data.list;

      // 更新分页信息
      pagination.itemCount = response.data.total;
      pagination.pageCount = response.data.pages;

      // 确保当前页码与API返回的一致
      if (pagination.page !== response.data.pageNum) {
        pagination.page = response.data.pageNum;
      }

      // 打印分页信息和数据信息，用于调试
      console.log("分页信息:", {
        total: response.data.total,
        pages: response.data.pages,
        pageNum: response.data.pageNum,
        navigatepageNums: response.data.navigatepageNums,
      });

      console.log("数据信息:", {
        dataLength: response.data.list.length,
        tableMaxHeight: tableMaxHeight.value,
        windowHeight: windowHeight.value,
      });

      // 如果当前页大于总页数且总页数不为0，则跳转到最后一页
      if (pagination.page > response.data.pages && response.data.pages > 0) {
        pagination.page = response.data.pages;
        refreshData();
        return;
      }
    } else {
      messages.error(response.message || "数据加载失败");
    }
  } catch (error) {
    console.error("加载数据失败:", error);
    messages.error("加载数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 处理查询
const handleSearch = () => {
  pagination.page = 1;
  refreshData();
};

// 显示新增对话框
const showAddDialog = () => {
  // 使用新的编辑弹窗组件
  currentEditId.value = null;
  editDialogVisible.value = true;
};

// 详情弹窗
const detailDialogVisible = ref(false);
const currentDetailId = ref(null);

// 编辑弹窗
const editDialogVisible = ref(false);
const currentEditId = ref(null);

// SKU选择器弹窗
const selectorVisible = ref(false);
const selectorParams = ref({});

// 处理SKU选择
const handleSkuSelected = (sku) => {
  messages.success(`已选择SKU: ${sku.skuId}`);
};

// 打开SKU选择器
const openSkuSelector = (params = {}) => {
  selectorParams.value = params;
  selectorVisible.value = true;
};

// 处理查看
const handleView = (id) => {
  currentDetailId.value = id;
  detailDialogVisible.value = true;
};

// 处理编辑
const handleEditSku = (id) => {
  currentEditId.value = id;
  editDialogVisible.value = true;
};

// 处理选择变化
const handleSelectionChange = (keys) => {
  selectedRows.value = skuData.value.filter((item) => keys.includes(item.id));
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page;
  refreshData();
};

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  refreshData();
};

// 处理导入成功
const handleImportSuccess = async (fileInfo) => {
  try {
    messages.success(`文件上传成功: ${fileInfo.fileName}`);

    // 调用车辆SKU的文件解析接口
    const response = await skuApi.importSku(fileInfo.fileKey);

    if (response.code === 200) {
      messages.success("正在处理数据，请稍后刷新数据列表");
      setTimeout(() => {
        refreshData();
      }, 10000);
    } else {
      messages.error(response.message || "文件解析失败");
    }
  } catch (error) {
    console.error("文件解析失败:", error);
    messages.error("文件解析失败，请检查文件格式是否正确");
  }
};

// 处理导入错误
const handleImportError = (errorMsg) => {
  console.error(`导入失败: ${errorMsg}`);
};
</script>

<style lang="scss" scoped>
@use "./ProductPage.scss";
</style>