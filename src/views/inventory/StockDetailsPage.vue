<template>
  <div class="stock-details-page">
    <!-- 筛选条件区域 -->
    <n-card class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">入库日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="filterForm.dateRange === 'custom'"
              v-model:value="filterForm.customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleCustomDateChange"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">车辆品牌</div>
          <div class="filter-options">
            <vehicle-brand-selector
              v-model="filterForm.vehicleCategory"
              @update:model-value="handleSearch"
            />
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-label">库存状态</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.stockStatus"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in stockStatusOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-label">调拨动态</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.transferStatus"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in transferStatusOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-label">库存类型</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.stockType"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in stockTypeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-label">库存单位</div>
          <div class="filter-options org-selector-container">
            <n-button
              type="primary"
              ghost
              @click="showOrgSelector = true"
              style="width: 350px; justify-content: flex-start"
            >
              <template #icon>
                <n-icon><Building /></n-icon>
              </template>
              {{ selectedOrgText }}
            </n-button>
            <n-button
              v-if="filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0"
              type="error"
              ghost
              size="small"
              @click="clearOrgSelection"
              style="margin-left: 8px"
            >
              清空
            </n-button>
            <!-- 显示选中的机构标签 - 在清空按钮右侧 -->
            <div
              v-if="filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0"
              class="selected-orgs-tags"
            >
              <n-tag
                v-for="org in filterForm.invoiceOrgs"
                :key="org.id"
                type="success"
                size="small"
                closable
                @close="removeOrg(org)"
                style="margin-left: 4px"
              >
                {{ org.orgName }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增库存
        </n-button>

        <!-- 调拨按钮 -->
        <n-button
          type="warning"
          @click="showInternalTransferDialog"
          :disabled="selectedRows.length === 0"
          round
        >
          <template #icon>
            <n-icon><SwapHorizontalOutline /></n-icon>
          </template>
          内部调拨
        </n-button>

        <n-button
          type="info"
          @click="showExternalTransferDialog"
          :disabled="selectedRows.length === 0"
          round
        >
          <template #icon>
            <n-icon><SwapHorizontalOutline /></n-icon>
          </template>
          外部调拨
        </n-button>

        <!-- 批量状态修改按钮 - 暂时隐藏 -->
        <!-- <n-button
          type="warning"
          @click="handleBatchChangeStatus"
          :disabled="selectedRows.length === 0"
          round
        >
          <template #icon>
            <n-icon><ContractOutline /></n-icon>
          </template>
          更新状态
        </n-button> -->

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="请输入VIN或车辆信息"
          style="width: 220px"
          clearable
          @keydown.enter="handleSearch"
          @clear="handleKeywordsClear"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="filteredData"
          :loading="loading"
          :row-key="(row) => row.id"
          :max-height="tableMaxHeight"
          virtual-scroll
          virtual-scroll-x
          :scroll-x="scrollX"
          :min-row-height="48"
          :height-for-row="() => 48"
          virtual-scroll-header
          :header-height="48"
          striped
          size="medium"
          :checked-row-keys="selectedRows.map((row) => row.id)"
          @update:checked-row-keys="handleRowSelection"
        />
      </div>

      <!-- 分页组件 - 固定在表格底部 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes"
          :item-count="pagination.itemCount"
          :show-size-picker="pagination.showSizePicker"
          :show-quick-jumper="pagination.showQuickJumper"
          size="medium"
          size-picker-option-text="条/页"
          page-size-option="每页"
          :prefix="() => `共 ${pagination.itemCount} 条`"
          :display-order="['prefix', 'pages', 'size-picker']"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <stock-form-modal
      v-model:visible="dialogVisible"
      :stock-id="currentStockId"
      :stock-data="currentStockData"
      :readonly="false"
      @success="handleFormSuccess"
    />

    <!-- 详情弹窗 -->
    <stock-form-modal
      v-model:visible="detailDialogVisible"
      :stock-id="currentDetailId"
      :stock-data="currentDetailData"
      :readonly="true"
      @success="handleDetailSuccess"
    />

    <!-- 业务机构选择器 -->
    <biz-org-selector
      v-model:visible="showOrgSelector"
      title="选择库存单位"
      business-permission="can_stock_in"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
    />

    <!-- 调拨对话框 -->
    <stock-transfer-modal
      v-model:visible="transferDialogVisible"
      :transfer-type="transferDialogTitle"
      :stock-ids="selectedRows.map((row) => row.id)"
      :selected-stocks="selectedRows"
      @success="handleTransferSuccess"
    />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from "vue";
import useStockDetailsPage from "./StockDetailsPage.js";
import VehicleBrandSelector from "@/components/common/VehicleBrandSelector.vue";
import StockFormModal from "@/components/inventory/StockFormModal.vue";
import StockTransferModal from "@/components/inventory/StockTransferModal.vue";

// 使用组合式函数获取所有页面逻辑
const {
  // 组件引用
  BizOrgSelector,

  // 图标
  SearchOutline,
  RefreshOutline,
  AddOutline,
  SwapHorizontalOutline,
  Building,

  // 响应式数据
  tableRef,
  loading,
  dialogVisible,
  currentStockId,
  currentStockData,
  stockStatusOptions,
  transferStatusOptions,
  stockTypeOptions,
  filterForm,
  pagination,
  detailDialogVisible,
  currentDetailId,
  currentDetailData,
  showOrgSelector,

  // 调拨相关状态
  transferDialogVisible,
  transferDialogTitle,
  selectedRows,

  // 计算属性
  tableMaxHeight,
  filteredData,
  columns,
  scrollX,
  selectedOrgText,

  // 日期相关
  dateRangeOptions,

  // 业务方法
  handleDateRangeChange,
  handleCustomDateChange,
  handleSearch,
  handleKeywordsClear,
  showAddDialog,
  handleFormSuccess,
  handleDetailSuccess,
  handlePageChange,
  handlePageSizeChange,
  refreshData,

  // 机构选择器相关方法
  handleOrgSelect,
  handleOrgCancel,
  clearOrgSelection,
  removeOrg,

  // 调拨相关方法
  handleRowSelection,
  showInternalTransferDialog,
  showExternalTransferDialog,
  handleTransferSuccess,

  // 状态修改相关方法
  // handleBatchChangeStatus, // 暂时隐藏

  // 生命周期方法
  initialize,
  cleanup,
} = useStockDetailsPage();

// 生命周期钩子
onMounted(() => {
  initialize();
});

onUnmounted(() => {
  cleanup();
});
</script>

<style lang="scss" scoped>
@use "./StockDetailsPage.scss";
</style>