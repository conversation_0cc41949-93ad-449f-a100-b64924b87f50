/* 出库单页面样式 */

.outbound-bill-page {
  padding: 16px;
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
  box-sizing: border-box;
}

.filter-card {
  margin-bottom: 0;
  flex-shrink: 0;
}

.toolbar {
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: flex-start;
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: 8px;
}

.custom-date-picker {
  width: 280px;
}

.org-selector-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.selected-orgs-tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
}

:deep(.department-selector) {
  width: 350px !important;
  min-width: 350px !important;
}

/* 自定义单选按钮组样式已移至全局样式文件 src/assets/styles/global.css */

/* 表格容器样式 */
.table-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  min-height: 0;
  max-height: 100%;
  width: 100%;
  overflow: hidden;
}

.data-table-wrapper {
  flex: 1;
  position: relative;
  width: 100%;
  min-height: 0;
  /* 确保flex子项可以收缩 */
  overflow: hidden;
  /* 防止表格溢出 */
}

/* 分页组件样式 - 固定在表格底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  padding: 10px 15px;
  background-color: #fafafa;
  // border-top: 1px solid #e0e0e6;
  flex-shrink: 0;
  overflow-x: auto;
  position: relative;
  z-index: 10;
  /* 确保分页组件在最上层 */
}

/* 分页组件内部样式优化 */
:deep(.pagination-container .n-pagination) {
  flex-wrap: nowrap;
  white-space: nowrap;
}

/* 隐藏表格容器的滚动条 */
:deep(.data-table-wrapper::-webkit-scrollbar) {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

:deep(.n-data-table) {
  height: 100% !important;
}

/* 虚拟滚动表头样式 */
:deep(.n-data-table .n-data-table-base-table-header) {
  background-color: #fafafa;
  border-bottom: 1px solid #e0e0e6;
}

:deep(.n-data-table .n-data-table-base-table-header .n-data-table-th) {
  background-color: #fafafa !important;
  text-align: center !important;
  font-weight: 600;
}

:deep(.n-data-table .n-data-table-base-table-header .n-data-table-th .n-data-table-th__title) {
  justify-content: center;
}

/* 数据单元格样式 */
:deep(.n-data-table .n-data-table-td) {
  text-align: center !important;
}

/* 弹窗样式 */
:deep(.n-card-header) {
  padding-top: 12px;
  padding-bottom: 12px;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: 12px;
  padding-bottom: 12px;
}