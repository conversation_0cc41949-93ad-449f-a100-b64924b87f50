<template>
  <div class="outbound-bill-page">
    <!-- 筛选条件区域 -->
    <n-card class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">出库日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="filterForm.dateRange === 'custom'"
              v-model:value="filterForm.customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleCustomDateChange"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">车辆品牌</div>
          <div class="filter-options">
            <vehicle-brand-selector
              v-model="filterForm.vehicleCategory"
              @update:model-value="handleSearch"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">出库状态</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.orderStatus"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in orderStatusOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-label">出库单位</div>
          <div class="filter-options org-selector-container">
            <n-button
              type="primary"
              ghost
              @click="showOrgSelector = true"
              style="width: 350px; justify-content: flex-start"
            >
              <template #icon>
                <n-icon><Building /></n-icon>
              </template>
              {{ selectedOrgText }}
            </n-button>
            <n-button
              v-if="filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0"
              type="error"
              ghost
              size="small"
              @click="clearOrgSelection"
              style="margin-left: 8px"
            >
              清空
            </n-button>
            <!-- 显示选中的机构标签 - 在清空按钮右侧 -->
            <div
              v-if="filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0"
              class="selected-orgs-tags"
            >
              <n-tag
                v-for="org in filterForm.invoiceOrgs"
                :key="org.id"
                type="success"
                size="small"
                closable
                @close="removeOrg(org)"
                style="margin-left: 4px"
              >
                {{ org.orgName }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新数据
        </n-button>

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="输入关键字进行搜索"
          style="width: 220px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="filteredData"
          :loading="loading"
          :row-key="(row) => row.id"
          :max-height="tableMaxHeight"
          virtual-scroll
          virtual-scroll-x
          :scroll-x="scrollX"
          :min-row-height="48"
          :height-for-row="() => 48"
          virtual-scroll-header
          :header-height="48"
          striped
          size="large"
        />
      </div>

      <!-- 分页组件 - 固定在表格底部 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes"
          :item-count="pagination.itemCount"
          :show-size-picker="pagination.showSizePicker"
          :show-quick-jumper="pagination.showQuickJumper"
          size="medium"
          size-picker-option-text="条/页"
          page-size-option="每页"
          :prefix="() => `共 ${pagination.itemCount} 条`"
          :display-order="['prefix', 'pages', 'size-picker']"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <order-edit-modal
      ref="orderEditModalRef"
      v-model:visible="dialogVisible"
      :is-edit="isEdit"
      :title="dialogTitle"
      :vehicle-category-options="
        vehicleCategoryOptions.filter((item) => item.value !== null)
      "
      :order-status-options="
        orderStatusOptions.filter((item) => item.value !== null)
      "
      @save="handleSaveSuccess"
      @cancel="dialogVisible = false"
    />

    <!-- 出库详情弹窗 -->
    <outbound-detail-modal
      v-model:visible="detailDialogVisible"
      :detail-data="currentDetailData"
    />

    <!-- 出库单组件 -->
    <outbound-bill-modal
      v-model:visible="outboundBillVisible"
      :order-id="currentOrderId"
      :outbound-id="currentOutboundId"
      :title="'出库单详情'"
      @save="handleOutboundBillSave"
      @cancel="outboundBillVisible = false"
    />

    <!-- 业务机构选择器 -->
    <biz-org-selector
      v-model:visible="showOrgSelector"
      title="选择出库单位"
      business-permission="can_stock_out"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
    />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from "vue";
import useStockOutboundBillPage from "./StockOutboundBillPage.js";
import VehicleBrandSelector from "@/components/common/VehicleBrandSelector.vue";
import OutboundBillModal from "@/components/inventory/OutboundBillModal.vue";
import { useDictOptions } from "@/utils/dictUtils";

// 使用组合式函数获取所有页面逻辑
const {
  // 组件引用
  OrderEditModal,
  BizOrgSelector,
  VehicleStocksSelector,
  OutboundDetailModal,
  SearchIcon,

  // 图标
  SearchOutline,
  RefreshOutline,
  CreateOutline,
  ExitOutline,
  Building,

  // 响应式数据
  tableRef,
  orderEditModalRef,
  loading,
  dialogVisible,
  dialogTitle,
  isEdit,
  vehicleSelectorVisible,
  outboundBillVisible,
  currentOutboundId,
  currentOutboundVin,
  currentOutboundSkuId,
  currentOrderId,
  orderStatusOptions,
  filterForm,
  ordersData,
  pagination,
  windowHeight,
  detailDialogVisible,
  currentDetailData,
  showOrgSelector,

  // 计算属性
  tableMaxHeight,
  filteredData,
  columns,
  scrollX,
  selectedOrgText,

  // 日期相关
  dateRangeOptions,

  // 业务方法
  handleDateRangeChange,
  handleCustomDateChange,
  handleSearch,
  handleEdit,
  handleViewDetail,
  handleSaveSuccess,
  handleSelectionChange,
  handlePageChange,
  handlePageSizeChange,
  handleOutbound,
  handleVehicleSelected,
  handleSelectorCancel,
  handleOutboundBillSave,
  refreshData,

  // 机构选择器相关方法
  handleOrgSelect,
  handleOrgCancel,
  clearOrgSelection,
  removeOrg,

  // 生命周期方法
  initialize,
  cleanup,
} = useStockOutboundBillPage();

// 为模态框提供品牌选项（不包含"不限"选项）
const { options: vehicleCategoryOptions } = useDictOptions(
  "vehicle_brand",
  false
);

// 生命周期钩子
onMounted(() => {
  initialize();
});

onUnmounted(() => {
  cleanup();
});
</script>

<style lang="scss" scoped>
@use "./StockOutboundBillPage.scss";
</style>