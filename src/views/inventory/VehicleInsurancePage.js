import { ref, reactive, computed, h } from 'vue'
import { NIcon, NTag, NButton, NSpace, NInputNumber, NSelect, useDialog } from 'naive-ui'
import {
  SearchOutline,
  RefreshOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  CreateOutline
} from '@vicons/ionicons5'
import messages from '@/utils/messages'
import { vehicleInsuranceApi } from '@/api/vehicleInsurance'
import { useDictOptions } from '@/utils/dictUtils'

export default function useVehicleInsurancePage() {
  // 初始化对话框
  const dialog = useDialog()
  window.$dialog = dialog

  // 状态变量
  const tableRef = ref(null)
  const loading = ref(false)
  const editableRowKeys = ref([]) // 当前可编辑的行的key集合
  const originalRowData = ref({}) // 存储编辑前的原始行数据
  const selectedRowKeys = ref([]) // 选中的行的key集合

  // 窗口尺寸响应式变量
  const windowHeight = ref(window.innerHeight)
  const windowWidth = ref(window.innerWidth)

  // 计算表格最大高度 - 用于虚拟滚动
  const tableMaxHeight = computed(() => {
    const screenHeight = windowHeight.value
    const pagepadding = 32 // 页面上下padding
    const filterHeight = 120 // 筛选区域高度
    const toolbarHeight = 60 // 工具栏高度
    const paginationHeight = 50 // 分页区域高度
    const margin = 20 // 额外边距

    return screenHeight - pagepadding - filterHeight - toolbarHeight - paginationHeight - margin
  })

  // 返利状态选项 - 使用响应式字典数据
  const { options: rebateStatusOptions } = useDictOptions('rebate_status', true)

  // 保险公司选项 - 使用响应式字典数据
  const { options: insuranceProviderOptions } = useDictOptions('insurance_provider', false)

  // 业务机构选择器相关状态
  const showOrgSelector = ref(false)
  const selectedOrgs = ref([])

  // 筛选表单
  const filterForm = reactive({
    keywords: '',
    salesOrgIds: [], // 销售单位ID数组
    rebateStatus: null, // 返利状态
    insuranceOrgIds: [] // 保险机构ID数组
  })

  // 数据列表
  const insuranceData = ref([])

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    itemCount: 0,
    showQuickJumper: false
  })

  // 返利状态映射 - 统一使用dictData提供的字典选项
  const rebateStatusMap = computed(() => {
    const map = {}
    rebateStatusOptions.value.forEach(option => {
      if (option.value) {
        map[option.value] = {
          text: option.label,
          type: option.type || 'default',
          color: option.color
        }
      }
    })
    return map
  })

  // 保险公司映射 - 统一使用dictData提供的字典选项
  const insuranceProviderMap = computed(() => {
    const map = {}
    insuranceProviderOptions.value.forEach(option => {
      if (option.value) {
        map[option.value] = {
          text: option.label,
          type: option.type || 'default',
          color: option.color
        }
      }
    })
    return map
  })

  // 计算滚动宽度 - 自适应屏幕宽度
  const scrollX = computed(() => {
    const minTableWidth = 1950 // 所有列的最小宽度总和（增加保险公司列120px）
    const availableWidth = windowWidth.value - 280 // 减去左侧菜单和边距

    // 如果可用宽度大于最小宽度，使用可用宽度；否则使用最小宽度
    return Math.max(minTableWidth, availableWidth)
  })

  // 选中机构的显示文本
  const selectedOrgText = computed(() => {
    if (selectedOrgs.value.length === 0) {
      return ''
    } else if (selectedOrgs.value.length === 1) {
      return selectedOrgs.value[0].orgName
    } else {
      return `已选择 ${selectedOrgs.value.length} 个机构`
    }
  })

  // 过滤后的数据
  const filteredData = computed(() => {
    return insuranceData.value
  })

  // 表格列配置
  const columns = [
    {
      type: 'selection',
      width: 80,
      align: 'center',
      disabled: (row) => {
        // 已返利状态的数据不可选择
        return row.rebateStatus === 'COMPLETED'
      }
    },
    {
      title: 'VIN',
      key: 'vin',
      width: 220,
      align: 'center',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 120,
      align: 'center',
      render(row) {
        if (!row.createTime) return '-'
        const date = new Date(row.createTime)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      }
    },
    {
      title: '客户姓名',
      key: 'customerName',
      width: 120,
      align: 'center',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '客户电话',
      key: 'customerMobile',
      width: 180,
      align: 'center',
      render(row) {
        return row.customerMobile || '-'
      }
    },
    {
      title: '车辆品牌',
      key: 'brand',
      width: 100,
      align: 'center'
    },
    {
      title: '车型系列',
      key: 'series',
      width: 250,
      align: 'center',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '保险公司',
      key: 'insuranceProvider',
      width: 220,
      align: 'center',
      render(row, index) {
        const isEditing = editableRowKeys.value.includes(row.id)
        if (isEditing) {
          return h('div', {
            style: {
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              width: '100%',
              padding: '2px'
            }
          }, [
            h(NSelect, {
              value: row.insuranceProvider,
              options: insuranceProviderOptions.value.map(option => ({
                label: option.label,
                value: option.value
              })),
              placeholder: '请选择保险公司',
              clearable: true,
              size: 'small',
              style: {
                width: '200px',
                minWidth: '200px'
              },
              onUpdateValue: (value) => {
                row.insuranceProvider = value
              }
            })
          ])
        }

        // 非编辑模式下的显示逻辑
        if (!row.insuranceProvider) {
          return h(
            NTag,
            {
              type: 'default',
              size: 'small',
              round: true,
              style: {
                padding: '0 8px',
                fontWeight: 'bold',
                color: '#909399'
              }
            },
            { default: () => '未指定' }
          )
        }

        const provider = insuranceProviderMap.value[row.insuranceProvider] || {
          text: row.insuranceProvider,
          type: 'default'
        }
        return h(
          NTag,
          {
            type: provider.type,
            size: 'small',
            round: true,
            style: {
              padding: '0 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => provider.text }
        )
      }
    },
    {
      title: '交强险保费(元)',
      key: 'ciFee',
      width: 160,
      align: 'center',
      render(row, index) {
        const isEditing = editableRowKeys.value.includes(row.id)
        if (isEditing) {
          return h(NInputNumber, {
            value: row.ciFee / 100,
            precision: 2,
            min: 0,
            buttonPlacement: 'both',
            step: 0.01,
            style: { width: '120px' },
            onUpdateValue: (value) => {
              row.ciFee = Math.round((value || 0) * 100)
            }
          })
        }
        const amountInYuan = row.ciFee / 100
        return `¥${amountInYuan.toFixed(2)}`
      }
    },
    {
      title: '交强险返利(元)',
      key: 'ciRebate',
      width: 160,
      align: 'center',
      render(row, index) {
        const isEditing = editableRowKeys.value.includes(row.id)
        if (isEditing) {
          return h(NInputNumber, {
            value: row.ciRebate / 100,
            precision: 2,
            min: 0,
            buttonPlacement: 'both',
            step: 0.01,
            style: { width: '120px' },
            onUpdateValue: (value) => {
              row.ciRebate = Math.round((value || 0) * 100)
            }
          })
        }
        const amountInYuan = row.ciRebate / 100
        return `¥${amountInYuan.toFixed(2)}`
      }
    },
    {
      title: '商业险保费(元)',
      key: 'biFee',
      width: 160,
      align: 'center',
      render(row, index) {
        const isEditing = editableRowKeys.value.includes(row.id)
        if (isEditing) {
          return h(NInputNumber, {
            value: row.biFee / 100,
            precision: 2,
            min: 0,
            buttonPlacement: 'both',
            step: 0.01,
            style: { width: '120px' },
            onUpdateValue: (value) => {
              row.biFee = Math.round((value || 0) * 100)
            }
          })
        }
        const amountInYuan = row.biFee / 100
        return `¥${amountInYuan.toFixed(2)}`
      }
    },
    {
      title: '商业险返利(元)',
      key: 'biRebate',
      width: 160,
      align: 'center',
      render(row, index) {
        const isEditing = editableRowKeys.value.includes(row.id)
        if (isEditing) {
          return h(NInputNumber, {
            value: row.biRebate / 100,
            precision: 2,
            min: 0,
            buttonPlacement: 'both',
            step: 0.01,
            style: { width: '120px' },
            onUpdateValue: (value) => {
              row.biRebate = Math.round((value || 0) * 100)
            }
          })
        }
        const amountInYuan = row.biRebate / 100
        return `¥${amountInYuan.toFixed(2)}`
      }
    },
    {
      title: '非车险保费(元)',
      key: 'oiFee',
      width: 160,
      align: 'center',
      render(row, index) {
        const isEditing = editableRowKeys.value.includes(row.id)
        if (isEditing) {
          return h(NInputNumber, {
            value: row.oiFee / 100,
            precision: 2,
            min: 0,
            buttonPlacement: 'both',
            step: 0.01,
            style: { width: '120px' },
            onUpdateValue: (value) => {
              row.oiFee = Math.round((value || 0) * 100)
            }
          })
        }
        const amountInYuan = row.oiFee / 100
        return `¥${amountInYuan.toFixed(2)}`
      }
    },
    {
      title: '非车险返利(元)',
      key: 'oiRebate',
      width: 160,
      align: 'center',
      render(row, index) {
        const isEditing = editableRowKeys.value.includes(row.id)
        if (isEditing) {
          return h(NInputNumber, {
            value: row.oiRebate / 100,
            precision: 2,
            min: 0,
            step: 0.01,
            buttonPlacement: 'both',
            style: { width: '120px' },
            onUpdateValue: (value) => {
              row.oiRebate = Math.round((value || 0) * 100)
            }
          })
        }
        const amountInYuan = row.oiRebate / 100
        return `¥${amountInYuan.toFixed(2)}`
      }
    },
    {
      title: '返利状态',
      key: 'rebateStatus',
      width: 120,
      align: 'center',
      render(row) {
        const status = rebateStatusMap.value[row.rebateStatus] || {
          text: row.rebateStatus || '未知',
          type: 'default'
        }
        return h(
          NTag,
          {
            type: status.type,
            size: 'small',
            round: true,
            style: {
              padding: '0 10px',
              fontWeight: 'bold'
            }
          },
          { default: () => status.text }
        )
      }
    },
    {
      title: '经办人',
      key: 'agentName',
      width: 120,
      align: 'center',
      render(row) {
        return row.agentName || '-'
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      align: 'center',
      render(row) {
        const isEditing = editableRowKeys.value.includes(row.id)

        if (isEditing) {
          return h(NSpace, { align: 'center' }, {
            default: () => [
              h(NButton, {
                quaternary: true,
                circle: true,
                size: 'small',
                type: 'success',
                onClick: () => saveRow(row),
                style: 'color: #18a058; font-size: 18px;'
              }, { default: () => h(NIcon, { component: CheckmarkCircleOutline }) }),
              h(NButton, {
                quaternary: true,
                circle: true,
                size: 'small',
                type: 'error',
                onClick: () => cancelEdit(row),
                style: 'color: #d03050; font-size: 18px;'
              }, { default: () => h(NIcon, { component: CloseCircleOutline }) })
            ]
          })
        }

        return h('div', {
          style: {
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%'
          }
        }, [
          h(NButton, {
            quaternary: true,
            circle: true,
            size: 'medium',
            onClick: () => editRow(row),
            style: {
              width: '36px',
              height: '36px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#666',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            },
            onMouseenter: (e) => {
              e.target.style.color = '#18a058'
              e.target.style.transform = 'scale(1.1)'
              e.target.style.backgroundColor = 'rgba(24, 160, 88, 0.1)'
            },
            onMouseleave: (e) => {
              e.target.style.color = '#666'
              e.target.style.transform = 'scale(1)'
              e.target.style.backgroundColor = 'transparent'
            }
          }, {
            default: () => h(NIcon, {
              component: CreateOutline,
              size: 20,
              style: {
                transition: 'inherit'
              }
            })
          })
        ])
      }
    }
  ]

  // 窗口大小变化处理函数
  const handleResize = () => {
    windowHeight.value = window.innerHeight
    windowWidth.value = window.innerWidth
  }

  // 刷新数据
  const refreshData = async () => {
    loading.value = true
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: pagination.pageSize
      }

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords
      }

      // 添加销售单位筛选
      if (filterForm.salesOrgIds.length > 0) {
        params.bizOrgIds = filterForm.salesOrgIds.join(',')
      }

      // 添加返利状态筛选
      if (filterForm.rebateStatus !== null) {
        params.rebateStatus = filterForm.rebateStatus
      }

      // 添加保险机构筛选
      if (filterForm.insuranceOrgIds.length > 0) {
        params.insuranceOrgIds = filterForm.insuranceOrgIds.join(',')
      }

      const response = await vehicleInsuranceApi.getInsuranceList(params)

      if (response.code === 200) {
        insuranceData.value = response.data.list || []
        pagination.itemCount = response.data.total || 0
      } else {
        messages.error(response.message || '获取车险数据失败')
      }
    } catch (error) {
      console.error('获取车险数据失败:', error)
      messages.error('获取车险数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 处理搜索
  const handleSearch = () => {
    pagination.page = 1
    refreshData()
  }

  // 处理页码变化
  const handlePageChange = (page) => {
    pagination.page = page
    refreshData()
  }

  // 处理每页条数变化
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    refreshData()
  }

  // 编辑行
  const editRow = (row) => {
    const rowKey = row.id

    // 在编辑前保存原始数据的深拷贝，用于取消编辑时恢复
    originalRowData.value[rowKey] = JSON.parse(JSON.stringify(row))

    // 设置为编辑状态
    editableRowKeys.value.push(rowKey)
  }

  // 取消编辑
  const cancelEdit = (row) => {
    const rowKey = row.id

    // 从可编辑行集合中移除
    editableRowKeys.value = editableRowKeys.value.filter(id => id !== rowKey)

    // 如果有原始数据，恢复原始数据
    const originalData = originalRowData.value[rowKey]
    if (originalData) {
      // 找到当前行在数据列表中的索引
      const index = insuranceData.value.findIndex(item => item.id === rowKey)
      if (index !== -1) {
        // 使用原始数据替换当前行
        insuranceData.value[index] = { ...originalData }
      }
      // 删除已使用的原始数据
      delete originalRowData.value[rowKey]
    }
  }

  // 保存行
  const saveRow = async (row) => {
    try {
      const rowKey = row.id

      // 准备更新数据
      const updateData = {
        ciFee: row.ciFee,
        ciRebate: row.ciRebate,
        biFee: row.biFee,
        biRebate: row.biRebate,
        oiFee: row.oiFee,
        oiRebate: row.oiRebate,
        insuranceProvider: row.insuranceProvider
      }

      const response = await vehicleInsuranceApi.updateInsurance(rowKey, updateData)

      if (response.code === 200) {
        messages.success('更新成功')

        // 清除原始数据缓存
        if (originalRowData.value[rowKey]) {
          delete originalRowData.value[rowKey]
        }

        // 从可编辑行集合中移除
        editableRowKeys.value = editableRowKeys.value.filter(id => id !== rowKey)

        // 更新本地数据
        const index = insuranceData.value.findIndex(item => item.id === rowKey)
        if (index !== -1) {
          if (response.data) {
            insuranceData.value[index] = { ...response.data }
          } else {
            insuranceData.value[index] = { ...row }
          }
        }
      } else {
        throw new Error(response?.message || '更新失败')
      }
    } catch (error) {
      console.error('保存失败:', error)
      messages.error(error.message || '保存失败')
    }
  }



  // 初始化
  const initialize = async () => {
    // 添加窗口大小变化监听器
    window.addEventListener('resize', handleResize)

    // 字典数据现在通过响应式方式自动加载
    // 加载初始数据
    await refreshData()
  }

  // 处理机构选择
  const handleOrgSelect = (orgs) => {
    if (orgs && orgs.length > 0) {
      // 多选模式，保存所有选中的机构
      selectedOrgs.value = [...orgs]
      filterForm.salesOrgIds = orgs.map(org => org.id)
      handleSearch()
    }
  }

  // 处理机构选择取消
  const handleOrgCancel = () => {
    showOrgSelector.value = false
  }

  // 移除选中的机构
  const removeOrg = (orgToRemove) => {
    const index = selectedOrgs.value.findIndex(org => org.id === orgToRemove.id)
    if (index > -1) {
      selectedOrgs.value.splice(index, 1)
      filterForm.salesOrgIds.splice(index, 1)
      handleSearch()
    }
  }

  // 处理表格行选择变化
  const handleSelectionChange = (keys) => {
    selectedRowKeys.value = keys
  }

  // 确认返利
  const confirmRebate = async () => {
    if (selectedRowKeys.value.length === 0) {
      messages.warning('请先选择要确认返利的数据')
      return
    }

    // 过滤出未返利状态的数据ID
    const validIds = selectedRowKeys.value.filter(id => {
      const row = insuranceData.value.find(item => item.id === id)
      return row && row.rebateStatus !== 'COMPLETED'
    })

    if (validIds.length === 0) {
      messages.warning('所选数据中没有可确认返利的记录')
      return
    }

    try {
      loading.value = true

      // 使用批量更新接口
      const response = await vehicleInsuranceApi.batchUpdateRebateStatus(validIds, 'COMPLETED')

      if (response.code === 200) {
        messages.success(`成功确认 ${validIds.length} 条记录的返利状态`)
        // 清空选中状态
        selectedRowKeys.value = []
        // 刷新数据
        refreshData()
      } else {
        messages.error(response.message || '批量更新返利状态失败')
      }
    } catch (error) {
      console.error('确认返利失败:', error)
      messages.error('确认返利失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 清理
  const cleanup = () => {
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', handleResize)
  }

  return {
    // 图标
    SearchOutline,
    RefreshOutline,
    CreateOutline,

    // 响应式数据
    tableRef,
    loading,
    editableRowKeys,
    rebateStatusOptions,
    insuranceProviderOptions,
    filterForm,
    insuranceData,
    pagination,
    windowHeight,
    windowWidth,
    showOrgSelector,
    selectedOrgs,
    selectedRowKeys,

    // 计算属性
    tableMaxHeight,
    filteredData,
    columns,
    scrollX,
    selectedOrgText,

    // 业务方法
    handleSearch,
    refreshData,
    handlePageChange,
    handlePageSizeChange,
    editRow,
    cancelEdit,
    saveRow,
    handleOrgSelect,
    handleOrgCancel,
    removeOrg,
    handleSelectionChange,
    confirmRebate,

    // 生命周期方法
    initialize,
    cleanup
  }
}
