import { ref, reactive, computed, h } from 'vue'
import { useMessage } from 'naive-ui'
import { NButton, NTag } from 'naive-ui'
import { useDictOptions } from '@/utils/dictUtils'
import skuApi from '@/api/sku'

/**
 * 新增销售限价规则逻辑
 */
export function useAddSalesPriceLimitRule() {
    const message = useMessage()

    // 状态变量
    const visible = ref(false)
    const formRef = ref(null)
    const detailLoading = ref(false)
    const saving = ref(false)
    const vehicleSelectorVisible = ref(false)

    // 表单数据
    const formData = reactive({
        ruleName: '',
        effectiveDate: null,
        expiryDate: null,
        ruleType: 'sku',
        priceLimit: null
    })

    // SKU表单数据
    const skuForm = reactive({
        vehicleBrand: null,
        vehicleModel: null,
        vehicleConfig: null
    })

    // 规则明细数据
    const ruleDetails = ref([])

    // 分页配置
    const pagination = reactive({
        page: 1,
        pageSize: 10,
        itemCount: 0,
        showSizePicker: true,
        pageSizes: [10, 20, 50],
        prefix: ({ itemCount }) => `共 ${itemCount} 条`
    })

    // 表单验证规则
    const formRules = {
        ruleName: [
            { required: true, message: '请输入规则名称', trigger: 'blur' }
        ],
        effectiveDate: [
            {
                required: true,
                message: '请选择生效日期',
                trigger: ['blur', 'change'],
                validator: (rule, value) => {
                    if (!value) {
                        return new Error('请选择生效日期')
                    }
                    return true
                }
            }
        ],
        expiryDate: [
            {
                required: true,
                message: '请选择失效日期',
                trigger: ['blur', 'change'],
                validator: (rule, value) => {
                    if (!value) {
                        return new Error('请选择失效日期')
                    }
                    // 检查失效日期是否晚于生效日期
                    if (formData.effectiveDate && value <= formData.effectiveDate) {
                        return new Error('失效日期必须晚于生效日期')
                    }
                    return true
                }
            }
        ],
        ruleType: [
            { required: true, message: '请选择规则类型', trigger: 'change' }
        ],
        priceLimit: [
            { required: true, message: '请输入销售限价', trigger: 'blur' },
            { type: 'number', min: 0, message: '限价金额不能小于0', trigger: 'blur' }
        ]
    }

    // 车辆品牌选项（使用响应式字典数据）
    const { options: vehicleBrandOptions } = useDictOptions('vehicle_brand', false)

    // 车型选项（从API获取）
    const vehicleModelOptions = ref([])

    // 配置选项（从API获取）
    const vehicleConfigOptions = ref([])

    // 加载状态
    const loadingModels = ref(false)
    const loadingConfigs = ref(false)

    // 明细表格列配置
    const detailColumns = computed(() => [
        {
            title: '品牌',
            key: 'brandName',
            width: 120,
            render: (row) => {
                return row.brandName || '-'
            }
        },
        {
            title: '车型',
            key: 'modelName',
            width: 150,
            render: (row) => {
                return row.modelName || '-'
            }
        },
        {
            title: '配置',
            key: 'configName',
            width: 200,
            render: (row) => {
                return row.configName || '-'
            }
        },
        {
            title: '在售车型数量',
            key: 'skuCount',
            width: 150,
            render: (row) => {
                if (row.type === 'sku') {
                    return `共${row.skuCount}个在售车型`
                } else {
                    return '-'
                }
            }
        },
        {
            title: '操作',
            key: 'actions',
            width: 100,
            render: (row, index) => {
                return h(
                    NButton,
                    {
                        size: 'small',
                        type: 'error',
                        text: true,
                        onClick: () => removeDetail(index)
                    },
                    { default: () => '删除' }
                )
            }
        }
    ])

    // 计算是否可以查询SKU
    const canQuerySku = computed(() => {
        return skuForm.vehicleBrand // 只需要选择品牌即可查询
    })

    // 获取车型选项
    const fetchVehicleModels = async (brand) => {
        if (!brand) {
            vehicleModelOptions.value = []
            return
        }

        try {
            loadingModels.value = true
            const response = await skuApi.getSkuOptions('series', { brand })

            if (response.code === 200) {
                // 去重并转换为选项格式
                const uniqueSeries = [...new Set(response.data.map(item => item.series).filter(Boolean))]
                vehicleModelOptions.value = uniqueSeries.map(series => ({
                    label: series,
                    value: series
                }))
            } else {
                message.error(response.message || '获取车型失败')
                vehicleModelOptions.value = []
            }
        } catch (error) {
            console.error('获取车型失败:', error)
            message.error('获取车型失败')
            vehicleModelOptions.value = []
        } finally {
            loadingModels.value = false
        }
    }

    // 获取配置选项
    const fetchVehicleConfigs = async (brand, series) => {
        if (!brand || !series) {
            vehicleConfigOptions.value = []
            return
        }

        try {
            loadingConfigs.value = true
            const response = await skuApi.getSkuOptions('configName', { brand, series })

            if (response.code === 200) {
                // 去重并转换为选项格式
                const uniqueConfigs = [...new Set(response.data.map(item => item.configName).filter(Boolean))]
                vehicleConfigOptions.value = uniqueConfigs.map(configName => ({
                    label: configName,
                    value: configName
                }))
            } else {
                message.error(response.message || '获取配置失败')
                vehicleConfigOptions.value = []
            }
        } catch (error) {
            console.error('获取配置失败:', error)
            message.error('获取配置失败')
            vehicleConfigOptions.value = []
        } finally {
            loadingConfigs.value = false
        }
    }

    // 处理品牌变化
    const handleBrandChange = (value) => {
        skuForm.vehicleModel = null
        skuForm.vehicleConfig = null
        vehicleConfigOptions.value = []

        // 获取车型选项
        if (value) {
            fetchVehicleModels(value)
        } else {
            vehicleModelOptions.value = []
        }
    }

    // 处理车型变化
    const handleModelChange = (value) => {
        skuForm.vehicleConfig = null

        // 获取配置选项
        if (value && skuForm.vehicleBrand) {
            fetchVehicleConfigs(skuForm.vehicleBrand, value)
        } else {
            vehicleConfigOptions.value = []
        }
    }

    // 查询SKU
    const querySku = async () => {
        if (!canQuerySku.value) {
            message.warning('请选择车辆品牌')
            return
        }

        try {
            detailLoading.value = true

            // 构建查询参数
            const queryParams = {
                page: 1,
                size: 1000, // 获取所有匹配的SKU
                brand: skuForm.vehicleBrand
            }

            // 如果选择了车型，添加到查询参数
            if (skuForm.vehicleModel) {
                queryParams.series = skuForm.vehicleModel
            }

            // 如果选择了配置，添加到查询参数
            if (skuForm.vehicleConfig) {
                queryParams.configName = skuForm.vehicleConfig
            }

            // 添加品牌权限限制
            const cachedBrands = vehicleBrandUtils.getCachedBrands()
            if (cachedBrands && cachedBrands.length > 0) {
                queryParams.brands = cachedBrands.join(',')
            }

            // 调用真实的SKU查询API
            const response = await skuApi.getSkuList(queryParams)

            if (response.code === 200) {
                // 按品牌、车型、配置分组
                const groupedData = new Map()

                response.data.list.forEach(sku => {
                    const key = `${sku.brand}-${sku.series || ''}-${sku.configName || ''}`

                    if (!groupedData.has(key)) {
                        groupedData.set(key, {
                            brandName: sku.brand,
                            modelName: sku.series || '',
                            configName: sku.configName || '',
                            skuIds: [],
                            skuCount: 0
                        })
                    }

                    const group = groupedData.get(key)
                    group.skuIds.push(sku.skuId)
                    group.skuCount++
                })

                // 转换为表格数据
                ruleDetails.value = Array.from(groupedData.values()).map((group, index) => ({
                    id: Date.now() + index,
                    type: 'sku',
                    brandName: group.brandName,
                    modelName: group.modelName,
                    configName: group.configName,
                    skuIds: group.skuIds.join(','), // 将ID数组转换为逗号分隔的字符串
                    skuCount: group.skuCount,
                    priceLimit: formData.priceLimit || 0
                }))

                pagination.itemCount = ruleDetails.value.length
                const totalSkuCount = response.data.list.length
                message.success(`查询到 ${ruleDetails.value.length} 个配置组合，共 ${totalSkuCount} 条SKU数据`)
            } else {
                message.error(response.data || '查询SKU失败')
                ruleDetails.value = []
                pagination.itemCount = 0
            }

        } catch (error) {
            console.error('查询SKU失败:', error)
            message.error('查询SKU失败')
            ruleDetails.value = []
            pagination.itemCount = 0
        } finally {
            detailLoading.value = false
        }
    }



    // 获取品牌名称（dictData中品牌值和标签相同）
    const getBrandName = (brandCode) => {
        // 在dictData中，品牌的optionValue和optionLabel是相同的
        return brandCode
    }

    // 显示车辆选择器
    const showVehicleSelector = () => {
        if (formData.priceLimit === null || formData.priceLimit <= 0) {
            message.warning('请先设置限价金额')
            return
        }
        vehicleSelectorVisible.value = true
    }

    // 处理车辆选择
    const handleVehicleSelected = (vehicles) => {
        vehicles.forEach(vehicle => {
            const newDetail = {
                id: Date.now() + Math.random(),
                type: 'vin',
                vin: vehicle.vin,
                brandName: vehicle.brandName || '',
                modelName: vehicle.modelName || '',
                priceLimit: formData.priceLimit
            }
            ruleDetails.value.push(newDetail)
        })

        pagination.itemCount = ruleDetails.value.length
        message.success(`已添加 ${vehicles.length} 条VIN规则`)
    }

    // 处理车辆选择器取消
    const handleVehicleSelectorCancel = () => {
        // 取消操作，不需要特殊处理
    }

    // 删除明细
    const removeDetail = (index) => {
        ruleDetails.value.splice(index, 1)
        pagination.itemCount = ruleDetails.value.length
        message.success('删除成功')
    }

    // 处理取消
    const handleCancel = () => {
        visible.value = false
        resetForm()
    }

    // 处理保存
    const handleSave = async () => {
        await saveRule(false)
    }

    // 处理保存并立即生效
    const handleSaveAndActivate = async () => {
        await saveRule(true)
    }

    // 保存规则
    const saveRule = async (activate = false) => {
        try {
            // 表单验证
            await formRef.value?.validate()

            if (ruleDetails.value.length === 0) {
                message.warning('请至少添加一条规则明细')
                return
            }

            saving.value = true

            // 构建保存数据 - 将分组数据展开为原始SKU数据
            const expandedRuleDetails = []
            ruleDetails.value.forEach(group => {
                if (group.type === 'sku' && group.skuIds) {
                    // 将逗号分隔的SKU ID字符串转换回数组，并为每个SKU创建一条记录
                    const skuIdArray = group.skuIds.split(',')
                    skuIdArray.forEach(skuId => {
                        expandedRuleDetails.push({
                            type: 'sku',
                            brandName: group.brandName,
                            modelName: group.modelName,
                            configName: group.configName,
                            skuId: skuId,
                            priceLimit: group.priceLimit
                        })
                    })
                } else {
                    // VIN类型数据直接添加
                    expandedRuleDetails.push(group)
                }
            })

            const saveData = {
                ...formData,
                ruleDetails: expandedRuleDetails,
                status: activate ? 'active' : 'draft'
            }

            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 1000))

            message.success(activate ? '规则已保存并立即生效' : '规则保存成功')
            visible.value = false
            resetForm()

        } catch (error) {
            console.error('保存失败:', error)
            if (error.message) {
                message.error(error.message)
            }
        } finally {
            saving.value = false
        }
    }

    // 重置表单
    const resetForm = () => {
        Object.assign(formData, {
            ruleName: '',
            effectiveDate: null,
            expiryDate: null,
            ruleType: 'sku',
            priceLimit: null
        })

        Object.assign(skuForm, {
            vehicleBrand: null,
            vehicleModel: null,
            vehicleConfig: null
        })

        ruleDetails.value = []
        pagination.itemCount = 0
    }

    return {
        // 状态
        visible,
        formRef,
        formData,
        formRules,
        skuForm,
        ruleDetails,
        detailLoading,
        saving,
        vehicleSelectorVisible,
        pagination,
        loadingModels,
        loadingConfigs,

        // 选项数据
        vehicleBrandOptions,
        vehicleModelOptions,
        vehicleConfigOptions,
        detailColumns,

        // 计算属性
        canQuerySku,

        // 方法
        handleCancel,
        handleSave,
        handleSaveAndActivate,
        handleBrandChange,
        handleModelChange,
        querySku,
        showVehicleSelector,
        handleVehicleSelected,
        handleVehicleSelectorCancel,
    }
}
