<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    title="新增规则"
    style="width: 1200px"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <!-- 基本信息 -->
      <n-card title="基本信息" class="form-section">
        <!-- 第一行：规则名称 + 销售限价 -->
        <div class="form-row">
          <n-form-item label="规则名称" path="ruleName" class="form-item">
            <n-input
              v-model:value="formData.ruleName"
              placeholder="请输入规则名称"
              maxlength="50"
              show-count
            />
          </n-form-item>

          <n-form-item label="销售限价" path="priceLimit" class="form-item">
            <n-input-number
              v-model:value="formData.priceLimit"
              placeholder="请输入限价金额"
              :precision="0"
              :min="0"
              :step="100"
              style="width: 100%"
              button-placement="both"
            >
              <template #prefix>￥</template>
            </n-input-number>
          </n-form-item>
        </div>

        <!-- 第二行：生效日期 + 失效日期 -->
        <div class="form-row">
          <n-form-item label="生效日期" path="effectiveDate" class="form-item">
            <n-date-picker
              v-model:value="formData.effectiveDate"
              type="date"
              placeholder="请选择生效日期"
              style="width: 100%"
            />
          </n-form-item>

          <n-form-item label="失效日期" path="expiryDate" class="form-item">
            <n-date-picker
              v-model:value="formData.expiryDate"
              type="date"
              placeholder="请选择失效日期"
              style="width: 100%"
            />
          </n-form-item>
        </div>

        <!-- 第三行：规则类型 + 空白占位 -->
        <div class="form-row">
          <n-form-item label="规则类型" path="ruleType" class="form-item">
            <n-radio-group v-model:value="formData.ruleType">
              <n-radio-button value="sku">SKU</n-radio-button>
              <n-radio-button value="vin">VIN</n-radio-button>
            </n-radio-group>
          </n-form-item>

          <div class="form-item">
            <!-- 空白占位 -->
          </div>
        </div>
      </n-card>

      <!-- 规则明细 -->
      <n-card title="规则明细" class="form-section">
        <!-- SKU模式的联动下拉框 -->
        <div v-if="formData.ruleType === 'sku'" class="sku-selector">
          <n-space align="end">
            <n-form-item label="品牌">
              <n-select
                v-model:value="skuForm.vehicleBrand"
                placeholder="请选择车辆品牌"
                style="width: 200px"
                :options="vehicleBrandOptions"
                clearable
                @update:value="handleBrandChange"
              />
            </n-form-item>

            <n-form-item label="车型">
              <n-select
                v-model:value="skuForm.vehicleModel"
                placeholder="请选择车型"
                style="width: 200px"
                :options="vehicleModelOptions"
                :disabled="!skuForm.vehicleBrand"
                :loading="loadingModels"
                clearable
                filterable
                @update:value="handleModelChange"
              />
            </n-form-item>

            <n-form-item label="配置">
              <n-select
                v-model:value="skuForm.vehicleConfig"
                placeholder="请选择配置"
                style="width: 200px"
                :options="vehicleConfigOptions"
                :disabled="!skuForm.vehicleModel"
                :loading="loadingConfigs"
                clearable
                filterable
              />
            </n-form-item>

            <n-form-item label=" ">
              <n-button
                type="primary"
                @click="querySku"
                :disabled="!canQuerySku"
              >
                查询
              </n-button>
            </n-form-item>
          </n-space>
        </div>

        <!-- VIN模式的车辆库存选择器 -->
        <div v-if="formData.ruleType === 'vin'" class="vin-selector">
          <n-button type="primary" @click="showVehicleSelector">
            选择车辆
          </n-button>
        </div>

        <!-- 规则明细表格 -->
        <div class="rule-details-table">
          <n-data-table
            :columns="detailColumns"
            :data="ruleDetails"
            :pagination="pagination"
            :loading="detailLoading"
            :row-key="(row) => row.id"
            striped
            flex-height
            style="min-height: 300px"
          />
        </div>
      </n-card>
    </n-form>

    <!-- 底部按钮 -->
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSave" :loading="saving">
          保存
        </n-button>
        <n-button
          type="success"
          @click="handleSaveAndActivate"
          :loading="saving"
        >
          立即生效
        </n-button>
      </n-space>
    </template>

    <!-- 车辆库存选择器 -->
    <vehicle-stocks-selector
      v-model:visible="vehicleSelectorVisible"
      :multiple="true"
      @confirm="handleVehicleSelected"
      @cancel="handleVehicleSelectorCancel"
    />
  </n-modal>
</template>

<script setup>
import { useAddSalesPriceLimitRule } from "./AddSalesPriceLimitRule.js";
import VehicleStocksSelector from "@/components/inventory/VehicleStocksSelector.vue";

// 使用组合式函数
const {
  // 状态
  visible,
  formRef,
  formData,
  formRules,
  skuForm,
  ruleDetails,
  detailLoading,
  saving,
  vehicleSelectorVisible,
  pagination,
  loadingModels,
  loadingConfigs,

  // 选项数据
  vehicleBrandOptions,
  vehicleModelOptions,
  vehicleConfigOptions,
  detailColumns,

  // 计算属性
  canQuerySku,

  // 方法
  handleCancel,
  handleSave,
  handleSaveAndActivate,
  handleBrandChange,
  handleModelChange,
  querySku,
  showVehicleSelector,
  handleVehicleSelected,
  handleVehicleSelectorCancel,
} = useAddSalesPriceLimitRule();

// 暴露方法给父组件
defineExpose({
  show: () => {
    visible.value = true;
  },
  hide: () => {
    visible.value = false;
  },
});
</script>

<style lang="scss" scoped>
.form-section {
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-item {
  flex: 1;
}

.sku-selector {
  margin-bottom: 16px;
}

.vin-selector {
  margin-bottom: 16px;
}

.rule-details-table {
  margin-top: 16px;
}
</style>
