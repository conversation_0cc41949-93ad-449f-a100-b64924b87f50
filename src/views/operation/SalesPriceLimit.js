import { ref, reactive, computed, markRaw } from 'vue'
import { useMessage } from 'naive-ui'
import {
    SearchOutline,
    RefreshOutline,
    AddOutline,
    CreateOutline,
    CheckmarkCircleOutline,
    CloseCircleOutline,
    ArchiveOutline,
    CopyOutline
} from '@vicons/ionicons5'
import SearchIcon from '@/components/icons/SearchIcon.vue'

import { dateRangeOptions, getDateRangeParams } from '@/utils/dateRange'
import { createSalesPriceLimitTableColumns } from './SalesPriceLimitColumns.js'

/**
 * 销售限价规则页面逻辑
 */
export function useSalesPriceLimitPage() {
    // 消息
    const message = useMessage()

    // 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
    const SearchOutlineIcon = markRaw(SearchOutline)
    const RefreshOutlineIcon = markRaw(RefreshOutline)
    const AddOutlineIcon = markRaw(AddOutline)
    const SearchIconComponent = markRaw(SearchIcon)
    const CreateOutlineIcon = markRaw(CreateOutline)
    const CheckmarkCircleOutlineIcon = markRaw(CheckmarkCircleOutline)
    const CloseCircleOutlineIcon = markRaw(CloseCircleOutline)
    const ArchiveOutlineIcon = markRaw(ArchiveOutline)
    const CopyOutlineIcon = markRaw(CopyOutline)

    // 状态变量
    const tableRef = ref(null)
    const loading = ref(false)
    const selectedRows = ref([])



    /**
     * 规则类型选项
     */
    const ruleTypeOptions = computed(() => [
        { label: '不限', value: null },
        { label: 'SKU', value: 'sku' },
        { label: 'VIN', value: 'vin' }
    ])

    /**
     * 规则状态选项
     */
    const ruleStatusOptions = computed(() => [
        { label: '不限', value: null },
        { label: '启用', value: 'active' },
        { label: '禁用', value: 'inactive' },
        { label: '草稿', value: 'draft' }
    ])



    // 筛选表单
    const filterForm = reactive({
        dateRange: null,
        customDateRange: null,
        vehicleBrand: null,
        ruleType: null,
        ruleStatus: null,
        keywords: ''
    })

    // 分页配置
    const pagination = reactive({
        page: 1,
        pageSize: 20,
        itemCount: 0,
        showSizePicker: true,
        showQuickJumper: true,
        pageSizes: [10, 20, 50, 100],
        prefix: ({ itemCount }) => `共 ${itemCount} 条`
    })

    // 模拟数据
    const salesPriceLimitData = ref([
        {
            id: 1,
            ruleName: '奔驰C级最低价限制',
            vehicleBrand: 'mercedes',
            vehicleModel: 'C级',
            ruleType: 'sku',
            limitType: 'min_price',
            minPrice: 25000000, // 以分为单位，250,000.00元
            maxPrice: null,
            ruleStatus: 'active',
            createdBy: '张三',
            createdAt: '2024-01-15 10:30:00',
            updatedAt: '2024-01-15 10:30:00',
            description: '奔驰C级车型最低销售价格不得低于25万元'
        },
        {
            id: 2,
            ruleName: 'BMW 3系价格区间限制',
            vehicleBrand: 'bmw',
            vehicleModel: '3系',
            ruleType: 'sku',
            limitType: 'price_range',
            minPrice: 28000000, // 280,000.00元
            maxPrice: 45000000, // 450,000.00元
            ruleStatus: 'active',
            createdBy: '李四',
            createdAt: '2024-01-14 14:20:00',
            updatedAt: '2024-01-16 09:15:00',
            description: 'BMW 3系车型销售价格必须在28-45万元区间内'
        },
        {
            id: 3,
            ruleName: '奥迪A4L最高价限制',
            vehicleBrand: 'audi',
            vehicleModel: 'A4L',
            ruleType: 'vin',
            limitType: 'max_price',
            minPrice: null,
            maxPrice: 40000000, // 400,000.00元
            ruleStatus: 'inactive',
            createdBy: '王五',
            createdAt: '2024-01-13 16:45:00',
            updatedAt: '2024-01-17 11:30:00',
            description: '奥迪A4L车型最高销售价格不得超过40万元'
        },
        {
            id: 4,
            ruleName: '丰田凯美瑞价格区间限制',
            vehicleBrand: 'toyota',
            vehicleModel: '凯美瑞',
            ruleType: 'sku',
            limitType: 'price_range',
            minPrice: 18000000, // 180,000.00元
            maxPrice: 28000000, // 280,000.00元
            ruleStatus: 'draft',
            createdBy: '赵六',
            createdAt: '2024-01-12 13:20:00',
            updatedAt: '2024-01-12 13:20:00',
            description: '丰田凯美瑞车型销售价格区间限制规则'
        },
        {
            id: 5,
            ruleName: '本田雅阁最低价限制',
            vehicleBrand: 'honda',
            vehicleModel: '雅阁',
            ruleType: 'vin',
            limitType: 'min_price',
            minPrice: 16000000, // 160,000.00元
            maxPrice: null,
            ruleStatus: 'active',
            createdBy: '孙七',
            createdAt: '2024-01-11 09:10:00',
            updatedAt: '2024-01-18 15:45:00',
            description: '本田雅阁车型最低销售价格限制'
        }
    ])

    // 计算过滤后的数据
    const filteredData = computed(() => {
        let data = [...salesPriceLimitData.value]

        // 关键字搜索
        if (filterForm.keywords) {
            const keyword = filterForm.keywords.toLowerCase()
            data = data.filter(item =>
                item.ruleName.toLowerCase().includes(keyword) ||
                item.vehicleModel.toLowerCase().includes(keyword) ||
                item.createdBy.toLowerCase().includes(keyword) ||
                item.description.toLowerCase().includes(keyword)
            )
        }

        // 车辆品牌筛选
        if (filterForm.vehicleBrand) {
            data = data.filter(item => item.vehicleBrand === filterForm.vehicleBrand)
        }

        // 规则类型筛选
        if (filterForm.ruleType) {
            data = data.filter(item => item.ruleType === filterForm.ruleType)
        }

        // 规则状态筛选
        if (filterForm.ruleStatus) {
            data = data.filter(item => item.ruleStatus === filterForm.ruleStatus)
        }



        // 日期范围筛选
        if (filterForm.dateRange) {
            const dateRange = getDateRangeParams(filterForm.dateRange, filterForm.customDateRange)
            if (dateRange.startDate || dateRange.endDate) {
                data = data.filter(item => {
                    const itemDate = new Date(item.createdAt).toISOString().split('T')[0]
                    if (dateRange.startDate && itemDate < dateRange.startDate) return false
                    if (dateRange.endDate && itemDate > dateRange.endDate) return false
                    return true
                })
            }
        }

        // 更新分页信息
        pagination.itemCount = data.length

        // 分页处理
        const start = (pagination.page - 1) * pagination.pageSize
        const end = start + pagination.pageSize
        return data.slice(start, end)
    })

    /**
     * 窗口高度计算
     */
    const windowHeight = ref(window.innerHeight)
    const tableMaxHeight = computed(() => {
        // 计算表格最大高度：窗口高度 - 头部 - 筛选区域 - 工具栏 - 分页 - 边距
        return windowHeight.value - 200
    })

    /**
     * 处理日期范围变化
     */
    const handleDateRangeChange = (value) => {
        filterForm.dateRange = value
        if (value !== 'custom') {
            filterForm.customDateRange = null
        }
        refreshData()
    }

    /**
     * 处理自定义日期范围变化
     */
    const handleCustomDateChange = (value) => {
        filterForm.customDateRange = value
        refreshData()
    }

    /**
     * 处理搜索
     */
    const handleSearch = () => {
        pagination.page = 1
        refreshData()
    }

    /**
     * 刷新数据列表
     */
    const refreshData = async () => {
        loading.value = true
        try {
            // 这里应该调用实际的API
            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 500))

            // 实际项目中这里会是API调用
            // const response = await salesPriceLimitApi.getList(params)
            // salesPriceLimitData.value = response.data

        } catch (error) {
            console.error('刷新数据失败:', error)
            message.error('刷新数据失败')
        } finally {
            loading.value = false
        }
    }



    /**
     * 处理选择变化
     */
    const handleSelectionChange = (keys) => {
        selectedRows.value = keys
    }

    /**
     * 处理页码变化
     */
    const handlePageChange = (page) => {
        pagination.page = page
    }

    /**
     * 处理页面大小变化
     */
    const handlePageSizeChange = (pageSize) => {
        pagination.pageSize = pageSize
        pagination.page = 1
    }

    /**
     * 复制到剪贴板
     */
    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text).then(() => {
            message.success('已复制到剪贴板')
        }).catch(() => {
            message.error('复制失败')
        })
    }

    /**
     * 查看详情
     */
    const handleView = (id) => {
        message.info(`查看规则详情: ${id}`)
    }

    /**
     * 编辑规则
     */
    const handleEdit = (id) => {
        message.info(`编辑规则: ${id}`)
    }

    /**
     * 状态变更
     */
    const handleStatusChange = (id, newStatus) => {
        const statusText = {
            'active': '启用',
            'inactive': '禁用',
            'draft': '草稿'
        }
        message.info(`${statusText[newStatus]}规则: ${id}`)
    }

    // 表格列配置
    const columns = computed(() => {
        const icons = {
            CopyOutlineIcon,
            SearchIconComponent,
            CreateOutlineIcon,
            CheckmarkCircleOutlineIcon,
            CloseCircleOutlineIcon,
            ArchiveOutlineIcon
        }

        const handlers = {
            copyToClipboard,
            handleView,
            handleEdit,
            handleStatusChange
        }

        return createSalesPriceLimitTableColumns(icons, handlers)
    })

    // 表格横向滚动宽度 - 根据实际列宽度计算
    const scrollX = computed(() => {
        // 计算所有列的总宽度
        // 选择列: 50px + 规则名称: 200px + 车辆品牌: 120px + 车型: 120px + 限价类型: 120px +
        // 最低价格: 140px + 最高价格: 140px + 规则状态: 100px + 创建人: 100px + 创建时间: 160px +
        // 更新时间: 160px + 规则描述: 200px + 操作: 200px
        // 总计: 50 + 200 + 120 + 120 + 120 + 140 + 140 + 100 + 100 + 160 + 160 + 200 + 200 = 1810
        return 1810
    })

    return {
        // 图标
        SearchOutlineIcon,
        RefreshOutlineIcon,
        AddOutlineIcon,
        SearchIconComponent,
        CreateOutlineIcon,
        CheckmarkCircleOutlineIcon,
        CloseCircleOutlineIcon,
        ArchiveOutlineIcon,
        CopyOutlineIcon,

        // 状态
        tableRef,
        loading,
        selectedRows,

        // 数据
        dateRangeOptions,
        ruleTypeOptions,
        ruleStatusOptions,
        filterForm,
        pagination,
        filteredData,
        columns,
        scrollX,
        tableMaxHeight,

        // 方法
        refreshData,
        handleDateRangeChange,
        handleCustomDateChange,
        handleSearch,
        handleSelectionChange,
        handlePageChange,
        handlePageSizeChange,
        copyToClipboard,
        handleView,
        handleEdit,
        handleStatusChange,
    }
}