.sales-price-limit-page {
    padding: 16px;
    height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: hidden;
    box-sizing: border-box;
}

.filter-card {
    margin-bottom: 0;
    flex-shrink: 0;
}

.toolbar {
    margin-bottom: 0;
    flex-shrink: 0;
}

.table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: center;
    flex-shrink: 0;
}

/* 筛选区域样式 */
.filter-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.filter-row {
    display: flex;
    align-items: flex-start;
}

.filter-label {
    width: 100px;
    flex-shrink: 0;
    font-weight: 500;
    line-height: 32px;
    color: #333;
}

.filter-options {
    flex: 1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

:deep(.n-radio-group) {
    flex-wrap: wrap;
}

:deep(.n-radio-button) {
    margin-bottom: 8px;
}

.custom-date-picker {
    width: 280px;
}

/* 自定义单选按钮组样式已移至全局样式文件 src/assets/styles/global.css */

/* 响应式设计 */
@media (max-width: 1200px) {
    .filter-label {
        width: 80px;
    }

    .custom-date-picker {
        width: 240px;
    }
}

@media (max-width: 768px) {
    .sales-price-limit-page {
        padding: 8px;
    }

    .filter-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .filter-label {
        width: auto;
        margin-bottom: 8px;
    }

    .filter-options {
        width: 100%;
    }

    .custom-date-picker {
        width: 100%;
    }
}

/* 表格样式优化 */
:deep(.n-data-table) {
    .n-data-table-th {
        font-weight: 600;
        background-color: #fafafa;
    }

    .n-data-table-td {
        border-bottom: 1px solid #f0f0f0;
    }

    .n-data-table-tr:hover {
        background-color: #f5f5f5;
    }
}

/* 工具栏样式 */
.toolbar {
    :deep(.n-button) {
        font-weight: 500;
    }

    :deep(.n-input) {
        .n-input__input-el {
            font-size: 14px;
        }
    }
}

/* 筛选卡片样式 */
.filter-card {
    :deep(.n-card-header) {
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;
    }

    :deep(.n-card__content) {
        padding-top: 16px;
    }
}

/* 分页样式 */
.pagination-container {
    :deep(.n-pagination) {
        .n-pagination-item {
            font-weight: 500;
        }

        .n-pagination-item--active {
            font-weight: 600;
        }
    }
}