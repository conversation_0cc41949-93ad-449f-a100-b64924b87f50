<template>
  <div class="sales-price-limit-page">
    <!-- 筛选条件区域 -->
    <n-card title="筛选条件" class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">创建日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="filterForm.dateRange === 'custom'"
              v-model:value="filterForm.customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleCustomDateChange"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">车辆品牌</div>
          <div class="filter-options">
            <vehicle-brand-selector
              v-model="filterForm.vehicleBrand"
              @update:model-value="handleSearch"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">规则类型</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.ruleType"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in ruleTypeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">规则状态</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.ruleStatus"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in ruleStatusOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><component :is="RefreshOutlineIcon" /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon><component :is="AddOutlineIcon" /></n-icon>
          </template>
          新增规则
        </n-button>

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="输入关键字进行搜索"
          style="width: 280px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><component :is="SearchOutlineIcon" /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 数据表格区域 -->
    <div class="table-container">
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="filteredData"
        :loading="loading"
        :pagination="pagination"
        :scroll-x="scrollX"
        :max-height="tableMaxHeight"
        :row-key="(row) => row.id"
        :checked-row-keys="selectedRows"
        @update:checked-row-keys="handleSelectionChange"
        striped
        flex-height
      />

      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :item-count="pagination.itemCount"
          :page-sizes="pagination.pageSizes"
          :show-size-picker="pagination.showSizePicker"
          :show-quick-jumper="pagination.showQuickJumper"
          :prefix="pagination.prefix"
          :display-order="['prefix', 'pages', 'size-picker']"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 新增规则组件 -->
    <add-sales-price-limit-rule ref="addRuleRef" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useSalesPriceLimitPage } from "./SalesPriceLimit.js";
import VehicleBrandSelector from "@/components/common/VehicleBrandSelector.vue";
import AddSalesPriceLimitRule from "./AddSalesPriceLimitRule.vue";

// 组件引用
const addRuleRef = ref(null);

// 使用页面逻辑
const {
  // 图标
  SearchOutlineIcon,
  RefreshOutlineIcon,
  AddOutlineIcon,

  // 状态
  tableRef,
  loading,

  // 数据
  dateRangeOptions,
  ruleTypeOptions,
  ruleStatusOptions,
  filterForm,
  pagination,
  filteredData,
  columns,
  scrollX,
  tableMaxHeight,
  selectedRows,

  // 方法
  refreshData,
  handleDateRangeChange,
  handleCustomDateChange,
  handleSearch,
  handleSelectionChange,
  handlePageChange,
  handlePageSizeChange,
} = useSalesPriceLimitPage();

// 显示新增对话框
const showAddDialog = () => {
  addRuleRef.value?.show();
};
</script>

<style lang="scss" scoped>
@use "./SalesPriceLimit.scss";
</style>