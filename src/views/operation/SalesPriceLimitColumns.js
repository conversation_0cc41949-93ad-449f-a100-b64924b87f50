import { h } from 'vue'
import { NIcon, NTag } from 'naive-ui'
import { vehicleBrandUtils } from '@/utils/dictUtils'

/**
 * 创建销售限价规则列表表格列配置
 * @param {Object} icons - 图标对象
 * @param {Object} handlers - 事件处理函数对象
 * @returns {Array} 表格列配置数组
 */
export function createSalesPriceLimitTableColumns(icons, handlers) {
  const {
    CopyOutlineIcon,
    SearchIconComponent,
    CreateOutlineIcon,
    CheckmarkCircleOutlineIcon,
    CloseCircleOutlineIcon,
    ArchiveOutlineIcon
  } = icons

  const {
    copyToClipboard,
    handleView,
    handleEdit,
    handleStatusChange
  } = handlers

  return [
    { type: 'selection', width: 50, fixed: 'left' },

    {
      title: '规则名称',
      key: 'ruleName',
      width: 200,
      fixed: 'left',
      ellipsis: {
        tooltip: true
      }
    },

    {
      title: '车辆品牌',
      key: 'vehicleBrand',
      width: 120,
      render(row) {
        const brandLabel = vehicleBrandUtils.getLabel(row.vehicleBrand)
        const brandColor = vehicleBrandUtils.getColor(row.vehicleBrand)
        const brandType = vehicleBrandUtils.getType(row.vehicleBrand)

        return h(
          NTag,
          {
            type: brandType,
            bordered: false,
            style: {
              color: brandColor,
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => brandLabel }
        )
      }
    },

    {
      title: '车型',
      key: 'vehicleModel',
      width: 120,
      ellipsis: {
        tooltip: true
      }
    },

    {
      title: '限价类型',
      key: 'limitType',
      width: 120,
      render(row) {
        const typeMap = {
          'min_price': { text: '最低价限制', type: 'info', color: '#2080f0' },
          'max_price': { text: '最高价限制', type: 'warning', color: '#f0a020' },
          'price_range': { text: '价格区间限制', type: 'success', color: '#18a058' }
        }

        const type = typeMap[row.limitType] || { text: '未知', type: 'default', color: '#909399' }

        return h(
          NTag,
          {
            type: type.type,
            bordered: false,
            style: {
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => type.text }
        )
      }
    },

    {
      title: '最低价格(元)',
      key: 'minPrice',
      width: 140,
      align: 'right',
      render(row) {
        if (row.minPrice === null || row.minPrice === undefined) {
          return h('span', { style: { color: '#909399' } }, '无限制')
        }
        // minPrice单位是分，需要转换为元
        const amountInYuan = row.minPrice / 100
        const formattedAmount = amountInYuan.toLocaleString('zh-CN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
        return h('span', { style: { fontWeight: 'bold', color: '#18a058' } }, `¥${formattedAmount}`)
      }
    },

    {
      title: '最高价格(元)',
      key: 'maxPrice',
      width: 140,
      align: 'right',
      render(row) {
        if (row.maxPrice === null || row.maxPrice === undefined) {
          return h('span', { style: { color: '#909399' } }, '无限制')
        }
        // maxPrice单位是分，需要转换为元
        const amountInYuan = row.maxPrice / 100
        const formattedAmount = amountInYuan.toLocaleString('zh-CN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
        return h('span', { style: { fontWeight: 'bold', color: '#f0a020' } }, `¥${formattedAmount}`)
      }
    },

    {
      title: '规则状态',
      key: 'ruleStatus',
      width: 100,
      render(row) {
        const statusMap = {
          'active': { text: '启用', type: 'success', color: '#18a058' },
          'inactive': { text: '禁用', type: 'error', color: '#d03050' },
          'draft': { text: '草稿', type: 'default', color: '#909399' }
        }

        const status = statusMap[row.ruleStatus] || statusMap['draft']

        return h(
          NTag,
          {
            type: status.type,
            bordered: false,
            style: {
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => status.text }
        )
      }
    },

    {
      title: '创建人',
      key: 'createdBy',
      width: 100
    },

    {
      title: '创建时间',
      key: 'createdAt',
      width: 160,
      render(row) {
        if (!row.createdAt) return '未设置'
        const date = new Date(row.createdAt)
        return date.toLocaleString('zh-CN')
      }
    },

    {
      title: '更新时间',
      key: 'updatedAt',
      width: 160,
      render(row) {
        if (!row.updatedAt) return '未更新'
        const date = new Date(row.updatedAt)
        return date.toLocaleString('zh-CN')
      }
    },

    {
      title: '规则描述',
      key: 'description',
      width: 200,
      ellipsis: {
        tooltip: true
      }
    },

    {
      title: '操作',
      key: 'actions',
      width: 200,
      align: 'center',
      fixed: 'right',
      render: (row) => {
        const actions = []

        // 查看按钮 - 所有状态都可以查看
        actions.push(
          h(
            'div',
            {
              style: {
                cursor: 'pointer',
                color: '#18a058',
                fontSize: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '8px'
              },
              onClick: () => handleView && handleView(row.id),
              title: '查看详情'
            },
            [h(SearchIconComponent, { size: 20, color: '#18a058' })]
          )
        )

        // 编辑按钮 - 草稿和启用状态可以编辑
        if (['draft', 'active'].includes(row.ruleStatus)) {
          actions.push(
            h(
              'div',
              {
                style: {
                  cursor: 'pointer',
                  color: '#18a058',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '8px'
                },
                onClick: () => handleEdit && handleEdit(row.id),
                title: '编辑规则'
              },
              [h(NIcon, { size: 20 }, { default: () => h(CreateOutlineIcon) })]
            )
          )
        }

        // 状态切换按钮
        if (row.ruleStatus === 'active') {
          // 禁用按钮
          actions.push(
            h(
              'div',
              {
                style: {
                  cursor: 'pointer',
                  color: '#d03050',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                },
                onClick: () => handleStatusChange && handleStatusChange(row.id, 'inactive'),
                title: '禁用规则'
              },
              [h(NIcon, { size: 20 }, { default: () => h(CloseCircleOutlineIcon) })]
            )
          )
        } else if (row.ruleStatus === 'inactive') {
          // 启用按钮
          actions.push(
            h(
              'div',
              {
                style: {
                  cursor: 'pointer',
                  color: '#18a058',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                },
                onClick: () => handleStatusChange && handleStatusChange(row.id, 'active'),
                title: '启用规则'
              },
              [h(NIcon, { size: 20 }, { default: () => h(CheckmarkCircleOutlineIcon) })]
            )
          )
        } else if (row.ruleStatus === 'draft') {
          // 发布按钮
          actions.push(
            h(
              'div',
              {
                style: {
                  cursor: 'pointer',
                  color: '#18a058',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                },
                onClick: () => handleStatusChange && handleStatusChange(row.id, 'active'),
                title: '发布规则'
              },
              [h(NIcon, { size: 20 }, { default: () => h(CheckmarkCircleOutlineIcon) })]
            )
          )
        }

        return h(
          'div',
          {
            style: {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }
          },
          actions
        )
      }
    }
  ]
}
