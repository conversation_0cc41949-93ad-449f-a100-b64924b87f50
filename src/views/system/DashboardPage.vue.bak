<template>
  <div class="map-chart" ref="mapEchart"></div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import * as echarts from "echarts";
import "echarts-gl"; //3D地图插件
  import axios from "axios";
const mapEchart = ref();
const chartDom = ref(null);
const isCity = ref(false);

// 处理地图数据

// 切换页面
const jumpToPage = (_) => {
  // 实现页面跳转后清除地图实例
  chartDom.value.clear();
}

/**
 * 初始化地图
 */

// 定义echarts方法
const chartMap = async () => {
  const myChart = echarts.init(mapEchart.value);// 初始化dom
  chartDom.value = myChart;
  initMap(myChart, "china", "100000"); // 初始化map
  myChart.on("click", (e) => { // 添加点击事件
    if(e.seriesType == "map3D"){
      const newName  = e.name;
      if (e.value.level === "district"){
        alert("该地区已经无法下钻");
        return;
      };
      historyMapData.value.push(e.value);// 添加历史记录
      initMap(myChart, newName, e.value.adcode);  // 初始化地图
    }else{
      jumpToPage(e);
    }
  });
  window.addEventListener("resize", () => {//让可视化地图跟随浏览器大小缩放
    myChart.resize();
  });
};

const initMap = async (chartDOM , geoName, adcode) => {// 初始化图表
  chartDOM.clear();// 清除echarts实例
  let mapData = await getMapJSON(adcode, geoName); // 请求map的json
  let plotData1 = [];//散点图数据1（可动态获取）
  let plotData2 = [];//散点图数据1（可动态获取）
  const option = getOption(geoName, mapData, plotData1, plotData2);// 图表配置项
  chartDOM.setOption(option);// 渲染配置
};

/**
 * 地图配置项
 */

// 请求地图json数据，并过滤成地图data配置项
const getMapJSON = async (adcode = 100000, geoName) => {
  const res = await axios.get(`https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=${adcode}_full`);
  echarts.registerMap(geoName,  res.data);  // 重新注册地图
  const mapData = res.data.features.map((item) => {// 过滤json数据
    return {
      value: item.properties,
      name: item.properties.name,
    };
  });
  return mapData;
};
// 图表生成配置项
/**
 * @description: 
 * @param {*} geoName：地图名称
 * @param {*} mapData：地图数据
 * @param {*} plotData1:散点数据
 * @param {*} plotData2:散点数据
 * @return {*}
 */
const getOption = (geoName, mapData, plotData1=[], plotData2=[]) => {
  // 图表配置项
  const option = {
    tooltip: {
      show: true, // 提示框
      trigger: "item",
      backgroundColor: 'rgba(255, 255, 255, 0)',
      borderWidth: 0,
      padding: 0,
      renderMode:'html',
      appendToBody:'true',
      className:'custom-tooltip-box',
      formatter: function (params) {
        let keys = Object.keys(params.data.value);
        let html;
        if(params.seriesType === 'map3D' && keys.includes('orgNum')){
        // 可在JS文件单独引入CSS文件(import 'xx.css';)，这里把CSS文件里定义的样式复制到style
       html = `<div class="custom-tooltip-box"  style="width: 263px;height: 170px;font-size: 18px;padding: 28px 20px;box-sizing: border-box;background-image: url('/modal-bk.png');background-size: 100% 100%;">`;
            html += `<div class="title" style="font-size: 25px;color:#29F1FAFF;line-height: 25px;font-size: 18px;">${params.name}</div>`;
            html += `<div class="row" style="margin-top: 10px;">`;
            html += `<span class="label" style="font-size: 18px;color: #fff;margin-right: 6px;">社区服务中心：</span>`;
            html += `<span style="color: #fff; font-size: 18px;" class="content">${params.value.communityNum}</span>`;
            html += `</div>`;
            html += `<div class="row" style="margin-top: 10px;">`;
            html += `<span class="label" style="font-size: 18px;color: #fff;margin-right: 6px;">养老服务机构：</span>`;
            html += `<span style="color: #fff; font-size: 18px;" class="content">${params.value.orgNum}</span>`;
            html += `</div>`;
            html += `<div class="row" style="margin-top: 10px;">`;
            html += `<span class="label" style="font-size: 18px;color: #fff;margin-right: 6px;">住宅小区：</span>`;
            html += `<span style="color: #fff; font-size: 18px;" class="content">${params.value.residenceNum}</span>`;
            html += `</div>`;
            html += `</div>`;
        }else if(params.seriesType === 'scatter3D' ) {
          html += `<div class="title" style="font-size: 25px;color:#fff; line-height: 25px;font-size: 18px;">${params.name}</div>`
        }else{
          html = ''
        }
        return html;
      },
   },
    geo3D: {
        zlevel: -10,
        map: geoName, // 地图类型。echarts-gl 中使用的地图类型同 geo 组件相同
        // data: mapData, //这里比较重要：获得过滤后的data，这样点击事件时就能获得这个data的值
        regionHeight: 5,//地图高度
        // top:60,
        // left:-30,  
        shading: "realistic",
        realisticMaterial: {
          detailTexture: "./1.png",
        },
        itemStyle: {
          borderWidth: 3,
          borderColor: "#33fefe",//地图边界颜色
          color: "#1970c3",
          opacity: 1,
        },
        label: {
          show: geoName == 'china' ? false : true, // 是否显示标签（地图区域名称）。
          color: "#fff", 
          fontSize: 14,
          formatter: (e) => {
            return ` ${e.name} `;
          },
        },
        emphasis: {
          label: { 
            show: true, 
            color:'#fff',
          },
          itemStyle: {
            // color: "transparent",
            color: "#043f93",
          },
        },
        light: {
          // 光照相关的设置。在 shading 为 'color' 的时候无效。  光照的设置会影响到组件以及组件所在坐标系上的所有图表。合理的光照设置能够让整个场景的明暗变得更丰富，更有层次。
          main: {
            // 场景主光源的设置，在 globe 组件中就是太阳光。
            color:'#33fefe',//地图边缘颜色
            intensity: 120, //主光源的强度。[ default: 1 ]
            shadow: true, //主光源是否投射阴影。默认关闭。    开启阴影可以给场景带来更真实和有层次的光照效果。但是同时也会增加程序的运行开销。
            shadowQuality: 'high',      // 阴影的质量。可选'low', 'medium', 'high', 'ultra' [ default: 'medium' ]
            alpha: 0, // 主光源绕 x 轴，即上下旋转的角度。配合 beta 控制光源的方向。[ default: 40 ]
            beta: 0, // 主光源绕 y 轴，即左右旋转的角度。[ default: 40 ]
          },
          ambient: {
            // 全局的环境光设置。
            color: '#fff', // 环境光的颜色。[ default: #fff ]
            intensity: 1, // 环境光的强度。[ default: 0.2 ]
          },
        },
        postEffect:{
          bloom:{
            enable:true,
            bloomIntensity:1,
          }
        },
        viewControl: {
        //   // 用于鼠标的旋转，缩放等视角控制。
        //   projection: 'perspective', // 投影方式，默认为透视投影'perspective'，也支持设置为正交投影'orthographic'。
        //   autoRotate: false, // 是否开启视角绕物体的自动旋转查看。[ default: false ]
        //   autoRotateDirection: 'cw', // 物体自传的方向。默认是 'cw' 也就是从上往下看是顺时针方向，也可以取 'ccw'，既从上往下看为逆时针方向。
        //   autoRotateSpeed: 10, // 物体自传的速度。单位为角度 / 秒，默认为10 ，也就是36秒转一圈。
        //   autoRotateAfterStill: 3, // 在鼠标静止操作后恢复自动旋转的时间间隔。在开启 autoRotate 后有效。[ default: 3 ]
        //   damping: 0, // 鼠标进行旋转，缩放等操作时的迟滞因子，在大于等于 1 的时候鼠标在停止操作后，视角仍会因为一定的惯性继续运动（旋转和缩放）。[ default: 0.8 ]
        //   rotateSensitivity: 1, // 旋转操作的灵敏度，值越大越灵敏。支持使用数组分别设置横向和纵向的旋转灵敏度。默认为1, 设置为0后无法旋转。	rotateSensitivity: [1, 0]——只能横旋转； rotateSensitivity: [0, 1]——只能纵向旋转。
        //   zoomSensitivity: 1, // 缩放操作的灵敏度，值越大越灵敏。默认为1,设置为0后无法缩放。
        //   panSensitivity: 1, // 平移操作的灵敏度，值越大越灵敏。默认为1,设置为0后无法平移。支持使用数组分别设置横向和纵向的平移灵敏度
        //   panMouseButton: 'left', // 平移操作使用的鼠标按键，支持：'left' 鼠标左键（默认）;'middle' 鼠标中键 ;'right' 鼠标右键(注意：如果设置为鼠标右键则会阻止默认的右键单。)
        //   rotateMouseButton: 'left', // 旋转操作使用的鼠标按键，支持：'left' 鼠标左键;'middle' 鼠标中键（默认）;'right' 鼠标右键(注意：如果设置为鼠标右键则会阻止默认的右键单。)
        distance: isCity ? 220 : 120, // [ default: 100 ] 默认视角距离主体的距离，对于 grid3D 和 geo3D 等其它组件来说是距离中心原点的距离,对于 globe 来说是距离地球表面的距离。在projection 为'perspective'的时候有效。
        minDistance: 60, // [ default: 40 ] 视角通过鼠标控制能拉近到主体的最小距离。在 projection 为'perspective'的时候有效。
        maxDistance: 280, // [ default: 400 ] 视角通过鼠标控制能拉远到主体的最大距离。在 projection 为'perspective'的时候有效。
        alpha: 90, // 视角绕 x 轴，即上下旋转的角度。配合 beta 可以控制视角的方向。[ default: 40 ]
        beta: 10, // 视角绕 y 轴，即左右旋转的角度。[ default: 0 ]
        // //   minAlpha: 25, // 上下旋转的最小 alpha 值。即视角能旋转到达最上面的角度。[ default: 5 ]
        // //   maxAlpha: 360, // 上下旋转的最大 alpha 值。即视角能旋转到达最下面的角度。[ default: 90 ]
        // //   minBeta: -360, // 左右旋转的最小 beta 值。即视角能旋转到达最左的角度。[ default: -80 ]
        // //   maxBeta: 360, // 左右旋转的最大 beta 值。即视角能旋转到达最右的角度。[ default: 80 ]
        // center: [-30, 60, 0], // 视角中心点，旋转也会围绕这个中心点旋转，默认为[0,0,0]。
        //   animation: true, // 是否开启动画。[ default: true ]
        //   animationDurationUpdate: 1000, // 过渡动画的时长。[ default: 1000 ]
        //   animationEasingUpdate: 'cubicInOut', // 过渡动画的缓动效果。[ default: cubicInOut ]
        },
        // itemStyle: {
        //   color: "transparent",
        //   shadowColor: 'rgba(22, 255, 255, 0.5)',
        //   shadowOffsetX:10,
        //   shadowOffsetY:10,
        //   shadowBlur: 10,
        // },
      
    },
    series: [
      {
        zlevel: -9,
        map: geoName, // 地图类型。echarts-gl 中使用的地图类型同 geo 组件相同
        type: "map3D",
        data: mapData,
        // regionHeight: 5,//地图高度   
        // top:60,
        // left:-30,     
        itemStyle: {
          color: "#1970c3",
          // borderWidth: 3,
          // borderColor: "#33fefe",//地图边界颜色
          // borderColor: "red",//地图边界颜色
          // opacity: 1,
          // shadowColor: 'rgba(22, 255, 255, 0.5)',
          // shadowOffsetX:10,
          // shadowOffsetY:10,
          // shadowBlur: 10,
        },
        emphasis: {
          label: { 
            show: false, 
            color:'#fff',
          },
          itemStyle: {
            color: "#1b8cd5",
          },
        },
        label: {
          show: geoName == 'china' ? false : true, // 是否显示标签（地图区域名称）。
          color: "#fff", 
          fontSize: 14,
          formatter: (e) => {
            return ` ${e.name} `;
          },
        },
        shading: "realistic",
        realisticMaterial: {
          detailTexture: "./1.png",
        },
        light: {
          // 光照相关的设置。在 shading 为 'color' 的时候无效。  光照的设置会影响到组件以及组件所在坐标系上的所有图表。合理的光照设置能够让整个场景的明暗变得更丰富，更有层次。
          main: {
            // 场景主光源的设置，在 globe 组件中就是太阳光。
            color:'#33fefe',//地图边缘颜色
            intensity: 120, //主光源的强度。[ default: 1 ]
            shadow: true, //主光源是否投射阴影。默认关闭。    开启阴影可以给场景带来更真实和有层次的光照效果。但是同时也会增加程序的运行开销。
            shadowQuality: 'high',      // 阴影的质量。可选'low', 'medium', 'high', 'ultra' [ default: 'medium' ]
            alpha: 0, // 主光源绕 x 轴，即上下旋转的角度。配合 beta 控制光源的方向。[ default: 40 ]
            beta: 0, // 主光源绕 y 轴，即左右旋转的角度。[ default: 40 ]
          },
          ambient: {
            // 全局的环境光设置。
            color: '#fff', // 环境光的颜色。[ default: #fff ]
            intensity: 1, // 环境光的强度。[ default: 0.2 ]
          },
        },
        postEffect:{
          bloom:{
            enable:true,
            bloomIntensity:1,
          }
        },
        viewControl: {
        //   // 用于鼠标的旋转，缩放等视角控制。
        //   projection: 'perspective', // 投影方式，默认为透视投影'perspective'，也支持设置为正交投影'orthographic'。
        //   autoRotate: false, // 是否开启视角绕物体的自动旋转查看。[ default: false ]
        //   autoRotateDirection: 'cw', // 物体自传的方向。默认是 'cw' 也就是从上往下看是顺时针方向，也可以取 'ccw'，既从上往下看为逆时针方向。
        //   autoRotateSpeed: 10, // 物体自传的速度。单位为角度 / 秒，默认为10 ，也就是36秒转一圈。
        //   autoRotateAfterStill: 3, // 在鼠标静止操作后恢复自动旋转的时间间隔。在开启 autoRotate 后有效。[ default: 3 ]
        //   damping: 0, // 鼠标进行旋转，缩放等操作时的迟滞因子，在大于等于 1 的时候鼠标在停止操作后，视角仍会因为一定的惯性继续运动（旋转和缩放）。[ default: 0.8 ]
        //   rotateSensitivity: 1, // 旋转操作的灵敏度，值越大越灵敏。支持使用数组分别设置横向和纵向的旋转灵敏度。默认为1, 设置为0后无法旋转。	rotateSensitivity: [1, 0]——只能横旋转； rotateSensitivity: [0, 1]——只能纵向旋转。
        //   zoomSensitivity: 1, // 缩放操作的灵敏度，值越大越灵敏。默认为1,设置为0后无法缩放。
        //   panSensitivity: 1, // 平移操作的灵敏度，值越大越灵敏。默认为1,设置为0后无法平移。支持使用数组分别设置横向和纵向的平移灵敏度
        //   panMouseButton: 'left', // 平移操作使用的鼠标按键，支持：'left' 鼠标左键（默认）;'middle' 鼠标中键 ;'right' 鼠标右键(注意：如果设置为鼠标右键则会阻止默认的右键单。)
        //   rotateMouseButton: 'left', // 旋转操作使用的鼠标按键，支持：'left' 鼠标左键;'middle' 鼠标中键（默认）;'right' 鼠标右键(注意：如果设置为鼠标右键则会阻止默认的右键单。)
        distance: isCity ? 220 : 120, // [ default: 100 ] 默认视角距离主体的距离，对于 grid3D 和 geo3D 等其它组件来说是距离中心原点的距离,对于 globe 来说是距离地球表面的距离。在projection 为'perspective'的时候有效。
        minDistance: 60, // [ default: 40 ] 视角通过鼠标控制能拉近到主体的最小距离。在 projection 为'perspective'的时候有效。
        maxDistance: 280, // [ default: 400 ] 视角通过鼠标控制能拉远到主体的最大距离。在 projection 为'perspective'的时候有效。
        alpha: 90, // 视角绕 x 轴，即上下旋转的角度。配合 beta 可以控制视角的方向。[ default: 40 ]
        beta: 10, // 视角绕 y 轴，即左右旋转的角度。[ default: 0 ]
        // //   minAlpha: 25, // 上下旋转的最小 alpha 值。即视角能旋转到达最上面的角度。[ default: 5 ]
        // //   maxAlpha: 360, // 上下旋转的最大 alpha 值。即视角能旋转到达最下面的角度。[ default: 90 ]
        // //   minBeta: -360, // 左右旋转的最小 beta 值。即视角能旋转到达最左的角度。[ default: -80 ]
        // //   maxBeta: 360, // 左右旋转的最大 beta 值。即视角能旋转到达最右的角度。[ default: 80 ]
        // center: [-30, 60, 0], // 视角中心点，旋转也会围绕这个中心点旋转，默认为[0,0,0]。
        //   animation: true, // 是否开启动画。[ default: true ]
        //   animationDurationUpdate: 1000, // 过渡动画的时长。[ default: 1000 ]
        //   animationEasingUpdate: 'cubicInOut', // 过渡动画的缓动效果。[ default: cubicInOut ]
        },
        // itemStyle: {
        //   color: "transparent",
        //   shadowColor: 'rgba(22, 255, 255, 0.5)',
        //   shadowOffsetX:10,
        //   shadowOffsetY:10,
        //   shadowBlur: 10,
        // },
      },
      {
        name: 'name1',
        type: 'scatter3D',
        zlevel: -8,
        coordinateSystem: 'geo3D',
        symbol:'path://M8.6669921875......',
        symbolSize:[30,26],
        silent: false ,
        data:plotData1, 
        itemStyle: {
          color: '#44f3fb',          
        },
        label:{
          show: false,
        },
        emphasis: {
          label: { 
            show: false, 
            color:'#fff',
          },
          itemStyle: {
            color: "#00ffc0",
          },
        },
      },
      {
        name: 'name2',
        type: 'scatter3D',
        zlevel: -8,
        coordinateSystem: 'geo3D',
        symbol:'path://path://M8.......',
        symbolSize:30,
        silent: false ,
        data:plotData2,
        tooltip: {
          show:true,
          trigger: 'item',
          itemStyle: {
            color: '#fff',
          },
          formatter: function (params) {
            return `<div class="title" style="font-size: 25px;color:#fff; line-height: 25px;font-size: 18px;">${params.name}</div>`
          },
        },
        itemStyle: {
          color: '#e7954d',
        },
        emphasis: {
          label: { 
            show: false, 
            color:'#fff',
          },
          itemStyle: {
            color: "#00ffc0",
          },
        },
      }
    ],
  };
  return option;
};

/**
 * 返回上级地图功能
 */

// 地图下钻历史记录
const historyMapData = ref([{ name: "china", adcode: 100000 }]);
// 返回上级地图


onMounted(() => {
  chartMap();// 挂载echart
});
</script>

<style>
  .map-chart {
    width: 100%;
    height: 90vh;
    position: relative;
  }
</style>