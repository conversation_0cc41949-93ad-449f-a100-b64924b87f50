<template>
  <div class="query-page">
    <!-- 筛选区域 -->
    <n-card v-if="hasFilters" class="filter-card">
      <template #header>
        <div class="card-header">
          <span>筛选条件</span>
          <n-button quaternary @click="toggleFilterCollapse">
            {{ isFilterCollapsed ? "展开" : "收起" }}
            <template #icon>
              <n-icon>
                <ChevronDown v-if="isFilterCollapsed" />
                <ChevronUp v-else />
              </n-icon>
            </template>
          </n-button>
        </div>
      </template>
      <n-form
        inline
        :model="filterForm"
        class="filter-form"
        @keyup.enter="handleFilter"
        :class="{ 'is-collapsed': isFilterCollapsed }"
      >
        <div class="filter-content" ref="filterContent">
          <n-grid :cols="24" :x-gap="12">
            <template v-for="field in visibleFilterFields" :key="field.name">
              <n-grid-item :span="getColSpan(field)">
                <n-form-item :label="field.label || field.name">
                  <!-- 字符串输入框 -->
                  <template v-if="field.type === 'string'">
                    <n-input
                      v-model:value="filterForm[field.name]"
                      :placeholder="'请输入' + (field.label || field.name)"
                      :style="{ width: field.width || '200px' }"
                    />
                  </template>

                  <!-- 数字输入框 -->
                  <template
                    v-else-if="
                      field.type === 'number' || field.type === 'currency'
                    "
                  >
                    <n-input-number
                      v-model:value="filterForm[field.name]"
                      :placeholder="'请输入' + (field.label || field.name)"
                      :precision="
                        field.decimals || (field.type === 'currency' ? 2 : 0)
                      "
                      :style="{ width: field.width || '200px' }"
                      clearable
                    />
                  </template>

                  <!-- 字典选择器 -->
                  <template v-else-if="field.type === 'dict'">
                    <n-select
                      v-model:value="filterForm[field.name]"
                      :placeholder="'请选择' + (field.label || field.name)"
                      :options="getDictOptions(field)"
                      :style="{ width: field.width || '200px' }"
                      :multiple="field.multiple"
                      clearable
                    />
                  </template>

                  <!-- 单选按钮组 -->
                  <template v-else-if="field.type === 'radio'">
                    <n-radio-group
                      v-model:value="filterForm[field.name]"
                      @update:value="handleFilterChange"
                      class="custom-radio-group"
                    >
                      <n-radio-button
                        v-for="option in field.options"
                        :key="option.value"
                        :value="option.value"
                        class="custom-radio-button"
                      >
                        {{ option.label }}
                      </n-radio-button>
                    </n-radio-group>
                  </template>

                  <!-- 日期选择器 -->
                  <template
                    v-else-if="
                      field.type === 'date' || field.type === 'datetime'
                    "
                  >
                    <n-date-picker
                      v-model:value="filterForm[`${field.name}Range`]"
                      :type="
                        field.type === 'date' ? 'daterange' : 'datetimerange'
                      "
                      :shortcuts="dateShortcuts"
                      clearable
                      :style="{ width: field.width || '280px' }"
                    />
                  </template>

                  <!-- 自定义组件插槽 -->
                  <template v-else-if="field.type === 'custom'">
                    <slot
                      :name="`filter-${field.name}`"
                      :field="field"
                      :value="filterForm[field.name]"
                      :update-value="
                        (val) => updateFilterValue(field.name, val)
                      "
                    >
                      <n-input
                        v-model:value="filterForm[field.name]"
                        :placeholder="'请输入' + (field.label || field.name)"
                        :style="{ width: field.width || '200px' }"
                      />
                    </slot>
                  </template>
                </n-form-item>
              </n-grid-item>
            </template>
          </n-grid>
        </div>
        <div class="filter-buttons">
          <n-button type="primary" @click="handleFilter" round>
            <template #icon>
              <n-icon><Search /></n-icon>
            </template>
            筛选
          </n-button>
          <n-button @click="resetFilter" round>
            <template #icon>
              <n-icon><Refresh /></n-icon>
            </template>
            重置
          </n-button>
        </div>
      </n-form>
    </n-card>

    <!-- 工具栏区域 -->
    <div class="toolbar">
      <n-space justify="space-between">
        <n-space>
          <!-- 默认按钮 -->
          <n-button type="primary" @click="refreshData" round>
            <template #icon>
              <n-icon><RefreshOutline /></n-icon>
            </template>
            刷新数据
          </n-button>

          <n-button
            v-if="config.actions?.add !== false"
            type="success"
            @click="handleAdd"
            round
          >
            <template #icon>
              <n-icon><AddOutline /></n-icon>
            </template>
            {{ config.actions?.addText || "新增" }}
          </n-button>

          <!-- 自定义按钮 -->
          <template v-for="button in config.buttons" :key="button.key">
            <n-button
              :type="button.type || 'default'"
              :disabled="
                button.disabled ||
                (button.requireSelection && selectedRows.length === 0)
              "
              @click="handleCustomAction(button)"
              round
            >
              <template #icon v-if="button.icon">
                <n-icon>
                  <component :is="button.icon" />
                </n-icon>
              </template>
              {{ button.text }}
            </n-button>
          </template>

          <!-- 自定义工具栏插槽 -->
          <slot
            name="toolbar-left"
            :selected-rows="selectedRows"
            :refresh="refreshData"
          />
        </n-space>

        <n-space>
          <!-- 搜索框 -->
          <n-input
            v-if="config.search !== false"
            v-model:value="filterForm.keywords"
            :placeholder="config.searchPlaceholder || '请输入关键词'"
            :style="{ width: config.searchWidth || '300px' }"
            clearable
            @keydown.enter="handleFilter"
            @clear="handleClear"
          >
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-input>

          <!-- 右侧工具栏插槽 -->
          <slot
            name="toolbar-right"
            :selected-rows="selectedRows"
            :refresh="refreshData"
          />
        </n-space>
      </n-space>
    </div>

    <!-- 数据表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="computedColumns"
          :data="tableData"
          :loading="loading"
          :row-key="config.rowKey || 'id'"
          :pagination="false"
          :scroll-x="config.scrollX || 1200"
          :max-height="tableMaxHeight"
          :sticky="true"
          @update:checked-row-keys="handleSelectionChange"
        />
      </div>

      <!-- 分页组件 - 固定在表格底部 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes"
          :item-count="pagination.itemCount"
          :show-size-picker="pagination.showSizePicker"
          :show-quick-jumper="pagination.showQuickJumper"
          size="medium"
          size-picker-option-text="条/页"
          page-size-option="每页"
          :prefix="() => `共 ${pagination.itemCount} 条`"
          :display-order="['prefix', 'pages', 'size-picker']"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 弹窗插槽 -->
    <slot name="modals" :refresh="refreshData" />
  </div>
</template>

<script setup>
import useQueryPage from "./QueryPage.js";

// Props定义
const props = defineProps({
  // 基础配置
  config: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  // 字段配置
  fields: {
    type: Array,
    required: true,
    default: () => [],
  },
  // API服务
  apiService: {
    type: Object,
    required: true,
  },
});

// Emits定义
const emit = defineEmits([
  "add",
  "edit",
  "delete",
  "view",
  "custom-action",
  "selection-change",
  "data-loaded",
]);

// 使用组合式函数获取所有逻辑
const {
  // 图标
  ChevronDown,
  ChevronUp,
  RefreshOutline,
  AddOutline,
  SearchOutline,

  // 响应式数据
  tableRef,
  filterContent,
  loading,
  tableData,
  selectedRows,
  isFilterCollapsed,
  filterForm,
  pagination,

  // 计算属性
  hasFilters,
  visibleFilterFields,
  tableMaxHeight,
  computedColumns,

  // 工具数据
  dateShortcuts,

  // 方法
  getDictOptions,
  getColSpan,
  updateFilterValue,
  toggleFilterCollapse,
  handleFilterChange,
  handleFilter,
  resetFilter,
  handleClear,
  refreshData,
  handleAdd,
  handleCustomAction,
  handleSelectionChange,
  handlePageChange,
  handlePageSizeChange,

  // 暴露给父组件的方法
  getSelectedRows,
  getFilterForm,
} = useQueryPage(props, emit);

// 暴露方法给父组件
defineExpose({
  refreshData,
  getSelectedRows,
  getFilterForm,
});
</script>

<style lang="scss">
@use "./QueryPage.scss";
</style>
