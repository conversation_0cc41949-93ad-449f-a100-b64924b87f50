<template>
  <div class="stock-page">
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增库存
        </n-button>
        <n-button type="error" @click="batchDelete" :disabled="!selectedRows.length" round>
          <template #icon>
            <n-icon><TrashOutline /></n-icon>
          </template>
          批量删除
        </n-button>
      </n-space>
      <n-space>
        <n-input v-model:value="searchKeyword" placeholder="输入关键词搜索" clearable>
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
        <n-button type="info" @click="handleSearch">
          搜索
        </n-button>
      </n-space>
    </n-space>

    <!-- 库存列表 -->
    <n-data-table
      ref="tableRef"
      :columns="columns"
      :data="filteredData"
      :loading="loading"
      :pagination="pagination"
      :row-key="row => row.id"
      @update:checked-row-keys="handleSelectionChange"
      @update:page="handlePageChange"
    />

    <!-- 新增/编辑对话框 -->
    <n-modal
      v-model:show="dialogVisible"
      :title="dialogTitle"
      preset="card"
      :style="{ width: '500px' }"
      :mask-closable="false"
    >
      <n-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="商品名称" path="productName">
          <n-input v-model:value="form.productName" placeholder="请输入商品名称" />
        </n-form-item>
        <n-form-item label="商品编码" path="productCode">
          <n-input v-model:value="form.productCode" placeholder="请输入商品编码" />
        </n-form-item>
        <n-form-item label="商品类别" path="category">
          <n-select
            v-model:value="form.category"
            :options="categoryOptions"
            placeholder="请选择商品类别"
          />
        </n-form-item>
        <n-form-item label="库存数量" path="quantity">
          <n-input-number
            v-model:value="form.quantity"
            :min="0"
            placeholder="请输入库存数量"
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="单价" path="price">
          <n-input-number
            v-model:value="form.price"
            :min="0"
            :precision="2"
            placeholder="请输入单价"
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="仓库位置" path="location">
          <n-input v-model:value="form.location" placeholder="请输入仓库位置" />
        </n-form-item>
        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="form.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="dialogVisible = false">取消</n-button>
          <n-button type="primary" @click="handleSave">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, h, computed, reactive } from 'vue'
import { useDialog, useMessage } from 'naive-ui'
import { RefreshOutline, AddOutline, TrashOutline, PencilOutline, SearchOutline } from '@vicons/ionicons5'
import { NButton, NSpace, NIcon, NDataTable, NModal, NForm, NFormItem, NInput, NInputNumber, NSelect } from 'naive-ui'

// 模拟数据
const mockData = [
  {
    id: 1,
    productName: '商品A',
    productCode: 'A001',
    category: '电子产品',
    quantity: 100,
    price: 199.99,
    location: '仓库A-01-01',
    remark: '热销商品',
    createTime: '2023-01-01 10:00:00',
    updateTime: '2023-01-10 15:30:00'
  },
  {
    id: 2,
    productName: '商品B',
    productCode: 'B002',
    category: '家居用品',
    quantity: 50,
    price: 299.99,
    location: '仓库B-02-03',
    remark: '限时促销',
    createTime: '2023-02-01 09:15:00',
    updateTime: '2023-02-15 11:20:00'
  },
  {
    id: 3,
    productName: '商品C',
    productCode: 'C003',
    category: '食品饮料',
    quantity: 200,
    price: 9.99,
    location: '仓库A-03-02',
    remark: '保质期至2023年底',
    createTime: '2023-03-05 14:20:00',
    updateTime: '2023-03-20 16:45:00'
  }
]

// 状态变量
const tableRef = ref(null)
const formRef = ref(null)
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('新增库存')
const isEdit = ref(false)
const selectedRows = ref([])
const searchKeyword = ref('')
const stockData = ref([...mockData])

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page) => {
    pagination.page = page
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
  }
})

// 表单数据
const form = ref({
  id: null,
  productName: '',
  productCode: '',
  category: null,
  quantity: 0,
  price: 0,
  location: '',
  remark: ''
})

// 表单验证规则
const rules = {
  productName: [
    { required: true, message: '请输入商品名称', trigger: 'blur' }
  ],
  productCode: [
    { required: true, message: '请输入商品编码', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择商品类别', trigger: 'change' }
  ],
  quantity: [
    { required: true, type: 'number', message: '请输入库存数量', trigger: 'change' }
  ],
  price: [
    { required: true, type: 'number', message: '请输入单价', trigger: 'change' }
  ]
}

// 商品类别选项
const categoryOptions = [
  { label: '电子产品', value: '电子产品' },
  { label: '家居用品', value: '家居用品' },
  { label: '食品饮料', value: '食品饮料' },
  { label: '服装鞋帽', value: '服装鞋帽' },
  { label: '办公用品', value: '办公用品' }
]

// 工具实例
const dialog = useDialog()
const message = useMessage()

// 表格列配置
const columns = [
  { type: 'selection', width: 50 },
  { title: 'ID', key: 'id', width: 80 },
  { title: '商品名称', key: 'productName', width: 150 },
  { title: '商品编码', key: 'productCode', width: 120 },
  { title: '商品类别', key: 'category', width: 120 },
  { 
    title: '库存数量', 
    key: 'quantity', 
    width: 100,
    render(row) {
      const color = row.quantity < 50 ? 'error' : (row.quantity < 100 ? 'warning' : 'success')
      return h('span', { style: { color: color === 'error' ? '#d03050' : color === 'warning' ? '#f0a020' : '#18a058' } }, row.quantity)
    }
  },
  { 
    title: '单价', 
    key: 'price', 
    width: 100,
    render(row) {
      return h('span', null, `¥${row.price.toFixed(2)}`)
    }
  },
  { title: '仓库位置', key: 'location', width: 150 },
  { title: '备注', key: 'remark', ellipsis: true },
  { 
    title: '创建时间', 
    key: 'createTime', 
    width: 180,
    sorter: (a, b) => new Date(a.createTime) - new Date(b.createTime)
  },
  { 
    title: '更新时间', 
    key: 'updateTime', 
    width: 180,
    sorter: (a, b) => new Date(a.updateTime) - new Date(b.updateTime)
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right',
    render(row) {
      return h(NSpace, { justify: 'center', size: 'small' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          onClick: () => handleEdit(row)
        }, {
          default: () => '编辑',
          icon: () => h(NIcon, null, { default: () => h(PencilOutline) })
        }),
        h(NButton, {
          size: 'small',
          type: 'error',
          onClick: () => handleDelete(row)
        }, {
          default: () => '删除',
          icon: () => h(NIcon, null, { default: () => h(TrashOutline) })
        })
      ])
    }
  }
]

// 根据搜索关键词过滤数据
const filteredData = computed(() => {
  if (!searchKeyword.value) return stockData.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return stockData.value.filter(item => 
    item.productName.toLowerCase().includes(keyword) ||
    item.productCode.toLowerCase().includes(keyword) ||
    item.category.toLowerCase().includes(keyword) ||
    item.location.toLowerCase().includes(keyword) ||
    item.remark.toLowerCase().includes(keyword)
  )
})

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})

// 获取库存数据
const fetchData = () => {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    stockData.value = [...mockData]
    loading.value = false
  }, 500)
}

// 刷新数据
const refreshData = () => {
  fetchData()
  message.success('数据已刷新')
}

// 显示新增对话框
const showAddDialog = () => {
  dialogTitle.value = '新增库存'
  isEdit.value = false
  form.value = {
    id: null,
    productName: '',
    productCode: '',
    category: null,
    quantity: 0,
    price: 0,
    location: '',
    remark: ''
  }
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (row) => {
  dialogTitle.value = '编辑库存'
  isEdit.value = true
  form.value = { ...row }
  dialogVisible.value = true
}

// 处理删除
const handleDelete = (row) => {
  dialog.warning({
    title: '警告',
    content: `确定要删除商品 "${row.productName}" 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      // 模拟删除操作
      stockData.value = stockData.value.filter(item => item.id !== row.id)
      message.success('删除成功')
    }
  })
}

// 批量删除
const batchDelete = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要删除的商品')
    return
  }
  
  dialog.warning({
    title: '警告',
    content: `确定要删除选中的 ${selectedRows.value.length} 个商品吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      // 模拟批量删除操作
      const ids = selectedRows.value.map(row => row.id)
      stockData.value = stockData.value.filter(item => !ids.includes(item.id))
      selectedRows.value = []
      message.success('批量删除成功')
    }
  })
}

// 处理保存
const handleSave = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) return
    
    // 模拟保存操作
    if (isEdit.value) {
      // 更新
      const index = stockData.value.findIndex(item => item.id === form.value.id)
      if (index !== -1) {
        stockData.value[index] = {
          ...form.value,
          updateTime: new Date().toLocaleString()
        }
        message.success('更新成功')
      }
    } else {
      // 新增
      const newId = Math.max(...stockData.value.map(item => item.id), 0) + 1
      const now = new Date().toLocaleString()
      stockData.value.push({
        ...form.value,
        id: newId,
        createTime: now,
        updateTime: now
      })
      message.success('添加成功')
    }
    
    dialogVisible.value = false
  })
}

// 处理选择变化
const handleSelectionChange = (keys) => {
  selectedRows.value = stockData.value.filter(item => keys.includes(item.id))
}

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
}
</script>

<style scoped>
.stock-page {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  margin-bottom: 16px;
}

.n-data-table {
  flex: 1;
}
</style>
