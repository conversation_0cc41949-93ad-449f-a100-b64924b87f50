<template>
  <div class="test-page">
    <h1>动态路由加载测试页面</h1>
    <n-card title="测试结果" class="test-card">
      <n-space vertical>
        <n-alert type="success" title="组件加载成功">
          如果你能看到这个页面，说明动态路由和组件加载功能正常工作！
        </n-alert>
        
        <n-divider />
        
        <div class="test-info">
          <h3>当前路由信息</h3>
          <pre>{{ routeInfo }}</pre>
        </div>
      </n-space>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { NCard, NSpace, NAlert, NDivider } from 'naive-ui'

const route = useRoute()

// 格式化路由信息
const routeInfo = computed(() => {
  return JSON.stringify({
    path: route.path,
    name: route.name,
    meta: route.meta,
    params: route.params,
    query: route.query
  }, null, 2)
})

onMounted(() => {
  console.log('测试页面已加载')
})
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.test-card {
  max-width: 800px;
  margin: 20px auto;
}

.test-info {
  margin-top: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
}
</style>
